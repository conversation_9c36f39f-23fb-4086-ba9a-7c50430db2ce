# encoding: utf-8
# @File  : readexecl.py
# @Author: liujq
# @Desc :
# @Date  :  2025/05/21 11:18

import pandas as pd

# 读取Excel文件，使用第一行作为列名
df = pd.read_excel('./新建订单.xlsx', sheet_name='Sheet1')

# 打印列名，以便检查
print("列名:", df.columns.tolist())

# 检查数据的前几行
print("\n数据预览:")
print(df.head())

# 按销售单号分组
try:
    grouped = df.groupby('销售单号')
    print(grouped)

    # 打印每个销售单号对应的商品信息
    print("\n按销售单号分组结果:")
    for name, group in grouped:
        print(f"\n销售单号: {name}")
        for index, row in group.iterrows():
            print(row['商品编码'])
            print(row['本次售价'])
            print(row['数量'])

except KeyError as e:
    print(f"\n错误: 找不到列 '{e}'")
    print("请确保Excel文件中包含'销售单号'列，或者检查列名是否有空格或特殊字符")