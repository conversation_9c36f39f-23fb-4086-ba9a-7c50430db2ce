# Order Automation Script

This script automates the process of creating orders on your company's website using data from an Excel file, and then updates the Excel file with the system-generated order numbers.

## Features

- Reads order data from an Excel file
- Handles multiple products per order
- Automates order creation on your company's website using Playwright
- Retrieves system-generated order numbers
- Updates the Excel file with the order numbers

## Requirements

- Python 3.7 or higher
- pandas
- playwright
- openpyxl

## Installation

1. Install the required packages:

```bash
pip install pandas playwright openpyxl
```

2. Install Playwright browsers:

```bash
python -m playwright install
```

## Configuration

Before running the script, update the following variables in `order_automation.py`:

- `EXCEL_FILE_PATH`: Path to your Excel file
- `WEBSITE_URL`: Your company's website URL
- `USERNAME`: Your login username
- `PASSWORD`: Your login password

## Excel File Format

Your Excel file should contain the following columns:

- 订单编码 (Order Code)
- 项目 (Project)
- 客户 (Customer)
- 收货人 (Recipient)
- 收货电话 (Phone)
- 省/直辖市 (Province)
- 城市 (City)
- 区/县 (District)
- 详细地址 (Address)
- 业务员 (Salesperson)
- 商务员 (Business Representative)
- 业务类型 (Business Type)
- 商品编号 (Product Code)
- 商品数量 (Quantity)
- 售价 (Price)
- 订单编号 (Order Number) - This column will be populated by the script

## Usage

Run the script with:

```bash
python order_automation.py
```

The script will:
1. Read the Excel file
2. Process each unique order code
3. Create orders on the website
4. Update the Excel file with the system-generated order numbers
5. Save the updated Excel file as `updated_[original_filename].xlsx`

## Customization

You'll need to customize the following functions based on your website's structure:

- `login()`: Update selectors for the login form
- `navigate_to_order_creation()`: Update navigation to the order creation page
- `fill_order_header()`: Update selectors for the order header form
- `add_product()`: Update selectors for adding products
- `submit_order()`: Update selectors for submitting the order and extracting the order number

## Troubleshooting

- If the script fails to find elements on the page, you may need to update the selectors
- If the script times out, you may need to increase the timeout values
- For debugging, set `headless=False` in the browser launch options to see the browser in action
