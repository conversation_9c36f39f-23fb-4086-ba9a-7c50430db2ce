# encoding: utf-8
# @File  : createproject.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/04/11 16:04
import requests

url = "https://lxerptest2.66123123.com/api/pomp-project-service/projectServerSetting/"
headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Authorization": "Bearer 224f389a-bf51-4d57-895d-ef7e731e3893",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/json",
}

def create_project(config_type, project_ids):
    # 循环遍历projectId列表
    for project_id in project_ids:
        data = {
            "configType": config_type,
            "ruleRelation": 0,
            "projectId": project_id,  # 替换projectId
            "actionBox": "dispatchOrder",
            "dispatchOrder": 1,
            "splitOrder": 0,
            "serverId": 1866,
            "description": "",
            "projectServerSettingRuleDimList": [
                {
                    "dimensionType": 1,
                    "dimensionValue": [
                        {
                            "firstLevelId": "1348",
                            "firstLevelName": "山东省",
                            "secondLevelId": "1360",
                            "secondLevelName": "青岛市",
                            # "thirdLevelId": "3239",
                            # "thirdLevelName": "东莞市"
                        }
                    ],
                    "operator": 0
                }
            ],
            "projectServerSettingRuleDimListTemp": [
                {
                    "dimensionType": 1,
                    "operator": 0,
                    "dimensionValue": [
                        {
                            "firstLevelId": "1348",
                            "firstLevelName": "山东省",
                            "secondLevelId": "1360",
                            "secondLevelName": "青岛市",
                            # "thirdLevelId": "3239",
                            # "thirdLevelName": "东莞市"
                        }
                    ],
                    "areaList": [
                        {
                            "areaId": "1935_2061_3239",
                            "areaName": "东莞市",
                            "areaType": 1,
                            "areaValue": "1935_2061_3239"
                        }
                    ],
                    "remoteList": {},
                    "categoryList": [
                        {
                            "categoryId": "1",
                            "categoryName": "快递"
                        }
                    ],
                    "selPack": [["1348_山东省", "1360_青岛市"]]
                }
            ],
            "validStartDate": "2025-04-01",
            "validEndDate": "2027-04-30"
        }

        response = requests.post(url, headers=headers, json=data)
        print(f"Project ID: {project_id}, Response: {response.json()}")

if __name__ == '__main__':
    # project_ids_0 = [2030, 112, 2034, 2039, 2032, 2026, 118, 779, 2042, 2044, 768, 1472, 2038, 2043]
    # project_ids_1 = [2044, 768, 1472, 2038, 2043]
    project_ids_0 = [2056,773,2052,110,118,2049,2051,2047,2054,2048,2055,2046,2053,2068,2050]
    project_ids_1 = [2048,2055,2046,2053,2068,2050]
    create_project(0, project_ids_0)
    create_project(1, project_ids_1)
