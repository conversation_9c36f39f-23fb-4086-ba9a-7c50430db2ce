import pytest
from playwright.sync_api import sync_playwright
from config.config import Config

@pytest.fixture(scope='session')
def browser():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(viewport={"width": 1920, "height": 1080})
        context1 = browser.new_context(viewport={"width": 1920, "height": 1080})
        page = context.new_page()
        page1 = context1.new_page()
        yield page, page1
        page.close()
        page1.close()
        browser.close()



@pytest.fixture(scope='session')
def api(playwright):
    request=playwright.request.new_context(base_url='https://ug.baidu.com/mcp')
    yield request
    request.dispose()

# @pytest.fixture(scope='session')
# def login(browser):
#     context = browser.new_context(viewport={"width": 1920, "height": 1080})
#     page = context.new_page()
#     yield page
#     page.close()

