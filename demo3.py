import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width":1280,"height":950})
    page = context.new_page()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")
    page.get_by_role("button", name="添 加").click()
    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增派单").click()
    page.locator(".el-input__inner").first.click()
    page.locator(".el-input__inner").first.fill("刘建强")
    page.get_by_text("刘建强专柜项目").click()
    page.locator("div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
    page.get_by_text("专柜测试2-建强").click()
    page.locator("div").filter(has_text=re.compile(r"^客户部门 专柜测试2-建强$")).get_by_placeholder("请输入").click()
    page.locator("ul").filter(has_text=re.compile(r"^专柜测试2-建强$")).locator("span").click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_role("option", name="刘建强公司  15010896747  北京市北京市朝阳区").click()
    page.get_by_role("row", name="商品合计： ￥0.00    应付款： ￥").get_by_placeholder("请输入").first.click()
    page.get_by_role("row", name="商品合计： ￥0.00    应付款： ￥").get_by_placeholder("请输入").first.fill("2")
    page.get_by_text("金典 Golden 10齿2寸*50支 10齿装订夹条2").nth(1).click()

    page.get_by_role("row", name="金典 Golden 10齿2寸*50支 10齿装订夹条2").get_by_placeholder("字数长度1~").nth(2).click()
    page.get_by_role("row", name="金典 Golden 10齿2寸*50支 10齿装订夹条2").get_by_placeholder("字数长度1~").nth(2).press("ArrowRight")
    page.get_by_role("row", name="金典 Golden 10齿2寸*50支 10齿装订夹条2").get_by_placeholder("字数长度1~").nth(2).fill("50")
    page.get_by_role("button", name="保存订单").click()
    page.get_by_role("button", name="确 定").click()
    page.get_by_text("派单管理").click()
    page.wait_for_timeout(1000)
    page.locator("//span[text()='派单']/ancestor::button").nth(1).click(force=True)
    page.get_by_role("radio", name="从全部中选择服务方").click()
    page.get_by_label("订单分派").get_by_placeholder("服务方").click()
    page.get_by_label("订单分派").get_by_placeholder("服务方").fill("集团")
    page.locator("span").filter(has_text=re.compile(r"^领先未来科技集团有限公司$")).click()
    page.get_by_role("button", name="确 定").click()
    page.get_by_label("接单中心").get_by_text("接单管理").click()
    page.get_by_role("button", name="查询结果").click()
    page.locator(".el-table__fixed-right > .el-table__fixed-body-wrapper > .el-table__body > tbody > tr > .el-table_1_column_2 > .cell > div > button:nth-child(2)").first.click()
    page.get_by_role("button", name="生成发货单", exact=True).click()
    page.get_by_role("button", name="确定").click()
    page.get_by_role("cell", name="").locator("i").click()
    page.get_by_role("button", name="出库").first.click()
    page.get_by_role("button", name="确 定").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
