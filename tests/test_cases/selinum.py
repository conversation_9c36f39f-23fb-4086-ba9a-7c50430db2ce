# encoding: utf-8
# @File  : selinum.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/03/27 16:04
import time

from playwright.sync_api import sync_playwright
from selenium import webdriver
from selenium.webdriver.common.by import By

# 第一个 driver（访问百度）
driver1 = webdriver.Chrome()
driver1.get("https://www.baidu.com")
driver2 = webdriver.Chrome()
driver2.get("https://playwright.dev/python/docs/browsers")
search_input=driver1.find_element(By.ID, "kw")
search_button=driver1.find_element(By.ID, "su")
search_values = ["Python", "Selenium", "Playwright", "WebDriver","oppo","华为","小米"]

# 循环输入值并点击搜索按钮
for value in search_values:

    search_input.clear()
    search_input.send_keys(value)
    search_button.click()
    time.sleep(2)
    # 打印当前页面标题
    print(f"搜索 '{value}' 后的页面标题: {driver1.title}")


# 操作 driver1 和 driver2 互不影响
print(driver1.title)
print(driver2.title)

driver1.quit()
driver2.quit()










