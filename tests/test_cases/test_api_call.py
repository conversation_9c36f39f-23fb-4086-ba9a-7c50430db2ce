# encoding: utf-8
# @File  : test_api_call.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/05/28 9:27
from playwright.sync_api import sync_playwright


def test_api_call(api):
    path='/pc/pcsearch'
    data={"invoke_info":{"pos_1":[{}],"pos_2":[{}],"pos_3":[{}]}}
    # headers={"Content-Type": "application/json",
    # "cookie":"BDUSS=ZBM0U3SnIzVk0wWlBiUzBBZHRoQUhrRGdOYmd0TXk1cVdIazVBRHVoMS16OFptRVFBQUFBJCQAAAAAAAAAAAEAAABn8qUWbGl1amlhbnFpYW5ndHYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH5Cn2Z-Qp9mam; BDUSS_BFESS=ZBM0U3SnIzVk0wWlBiUzBBZHRoQUhrRGdOYmd0TXk1cVdIazVBRHVoMS16OFptRVFBQUFBJCQAAAAAAAAAAAEAAABn8qUWbGl1amlhbnFpYW5ndHYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH5Cn2Z-Qp9mam; BAIDUID=938F11781B09E41352696702895531C5:FG=1; PSTM=1731995767; BIDUPSID=C207182911BDB908B0C8B92CC230BD8C; H_WISE_SIDS_BFESS=61027_61803_61986_62056_62068; MAWEBCUID=web_CuIHBZHrNpWboleZZnyWPqJQVtifGVNaezjhnVhZtQpYsrHrPl; MCITY=-%3A; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; BA_HECTOR=ak2581248l008k2ha50k8h80a02k2m1k3a69e24; ZFY=VeJzEoEYf2AMtzFvixRFFiaugIdl1Xm4dXq34KJmErY:C; BAIDUID_BFESS=938F11781B09E41352696702895531C5:FG=1; H_WISE_SIDS=62325_62969_63194_63243_63248_63266_63354_63363_63389_63380; H_PS_PSSID=62325_62969_63140_63194_63211_63243_63248_63256_63266_63326_63354_63363_63389_63380_63186_63395_63391_63404; delPer=0; PSINO=2"}
    #response = api.post(url=path, data=data,  headers=headers)
    response = api.post(url=path, data=data)
    response = api.post(url=path, form=data)
    response = api.post(url=path, multipart=data)

    print(response.text())
    print(response.json())
    #assert response.status_code == 200

