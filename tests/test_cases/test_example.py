import pytest
from utils.data_loader import load_test_data
from utils.report_utils import attach_screenshot
import time
import datetime
@pytest.mark.parametrize('test_data', ["苹果","香梨","香蕉"])
#@pytest.mark.parametrize('test_data', ["苹果"])
def test_example(browser, test_data):
    # page = login
    starttime=datetime.datetime.now()
    page,page1 = browser
    page.goto("https://www.baidu.com/")


    page.locator("#kw").click()
    page.locator("#kw").fill(test_data)
    page.wait_for_timeout(1000)
    page.get_by_role("button", name="百度一下").click()
    page1.goto("https://playwright.dev/python/docs/browsers")
    page.wait_for_timeout(1000)
    #page1.locator("//a[@class='menu__link']").nth(1).click()
    page1.get_by_text("Extensibility").click()
    page.wait_for_timeout(1000)
    endtime = datetime.datetime.now()
    print("Start Time:", starttime)  # 添加调试信息
    print("End Time:", endtime)      # 添加调试信息
    print(endtime-starttime)
    # rows=page.locator("//td[@class='el-table__cell el-table__expanded-cell']//tr[@class='el-table__row']")
    # for i in range(rows.count()):
    #     row = rows.nth(i)
    #     row.locator("//td[last()-2]").click()

    
