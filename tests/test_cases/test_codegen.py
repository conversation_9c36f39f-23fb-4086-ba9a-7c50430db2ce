# encoding: utf-8
# @File  : test_codegen.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/03/23 11:05
import time

from playwright.async_api import expect
import datetime
import re
def test_codegen(browser):
    page,_=browser
    starttime=datetime.datetime.now()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")
    page.get_by_role("button", name="添 加").click()
    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增接单").click()
    page.wait_for_selector(".el-input__inner")
    page.locator(".el-input__inner").first.click()
    page.locator(".el-input__inner").first.fill("刘建强")
    page.get_by_text("刘建强专柜项目").click()
    #xpath语法//div[@class='chunk' ]/span[contains(text(),'客户')]/following-sibling::div[@class='el-form-item is-required']
    page.locator(
        "div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
    page.get_by_text("专柜测试2-建强").click()
    # page.pause()
    #classname = page.locator("//div[@class='cell']/span[contains(text(),'本次折扣率%')]/ancestor::th[1]").get_attribute("class")
    classname = page.locator("//div[@class='cell']/span[contains(text(),'本次折扣率%')]/ancestor::th[1]")
    name=classname.first.get_attribute("class")
    print(name)
    # classname = page.locator("//div[@class='cell']/span[contains(text(),'本次折扣率%')]/ancestor::th[1]").evaluate("el => el.className")
    # print(classname)
    # classname = page.locator("//div[@class='cell']/span[contains(text(),'本次折扣率%')]/ancestor::th[1]").evaluate(
    #     '(element) => element.getAttribute("class")')



    page.locator("div").filter(has_text=re.compile(r"^客户部门 专柜测试2-建强$")).get_by_placeholder("请输入").click()
    page.locator("ul").filter(has_text=re.compile(r"^专柜测试2-建强$")).locator("span").click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_role("option", name="刘建强公司  15010896747  北京市北京市朝阳区").click()
    page.get_by_role("row", name="--").get_by_placeholder("请输入").first.click()
    page.get_by_role("row", name="--").get_by_placeholder("请输入").first.fill("1")
    page.get_by_text("日丰 PP-R 32*1/2 32*1/2 外丝直接").nth(1).click()
    page.get_by_role("button", name="保存订单").click()
    #
    # # time.sleep(6)
    page.wait_for_selector("//span[@class='el-dialog__title' and normalize-space(text())='下单成功']")
    print(page.get_by_label("下单成功").inner_text())
    print(page.get_by_role("button", name="复制单号").count())
    # button = page.get_by_role("button", name="复制单号")
    # print("Visible:", button.is_visible())
    # print("Enabled:", button.is_enabled())
    # print("BoundingBox:", button.bounding_box())
    # #expect(page.get_by_role("button", name="复制单号")).to_be_visible()
    # expect(page.get_by_role("button", name="复制单号")).to_be_attached()


    # expect(page.get_by_label("下单成功")).to_contain_text("下单成功")

    assert page.get_by_role("button", name="复制单号").is_visible()
    page.get_by_role("button", name="确 定").click()
    endtime=datetime.datetime.now()
    print(endtime-starttime)

