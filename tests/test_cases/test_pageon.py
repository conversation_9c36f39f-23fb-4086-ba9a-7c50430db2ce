# encoding: utf-8
# @File  : test_pageon.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/03/21 20:51
import re
from playwright.sync_api import Playwright, sync_playwright, expect


def test_run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width":1280,"height":950})
    page = context.new_page()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")

    page.on("response", lambda response: print(response.json()))
    page.get_by_role("menuitem", name="订单").click()

    print("结束信息")

    # ---------------------
    context.close()
    browser.close()


# with sync_playwright() as playwright:
#     run(playwright)