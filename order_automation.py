# encoding: utf-8
# @File  : order_automation.py
# @Desc  : 自动创建订单并将订单号更新到Excel文件

import pandas as pd
import asyncio
from playwright.async_api import async_playwright
import time
import os
import re

# 配置信息 - 请更新以下值
EXCEL_FILE_PATH = './order_data 0521.xlsx'  # Excel文件路径
WEBSITE_URL = 'https://lxerptest2.66123123.com/'  # 替换为您公司的网站URL
USERNAME = 'liujianqiang'  # 替换为您的登录用户名
PASSWORD = 'a111111'  # 替换为您的登录密码

async def main():
    # 读取Excel文件
    print(f"正在读取Excel文件: {EXCEL_FILE_PATH}")
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        print(f"成功读取Excel文件，共{len(df)}行数据")
        print("Excel中的列名:", df.columns.tolist())
    except Exception as e:
        print(f"读取Excel文件出错: {e}")
        return

    # 检查必要的列是否存在
    required_columns = ['订单编码', '项目', '客户', '收货人', '收货电话', '省/直辖市', '城市',
                        '区/县', '详细地址', '业务员', '商务员', '业务类型',
                        '商品编号', '商品数量', '售价']

    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"缺少必要的列: {missing_columns}")
        return

    # 确保'订单编号'列存在，如果不存在则添加
    if '订单编号' not in df.columns:
        df['订单编号'] = ''
        print("已添加'订单编号'列到数据表中")

    # 按订单编码分组
    order_groups = df.groupby('订单编码')
    print(f"找到{len(order_groups)}个不同的订单需要处理")

    # 初始化Playwright
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 生产环境中设置headless=True
        context = await browser.new_context()
        page = await context.new_page()

        # 导航到网站
        print(f"正在导航到 {WEBSITE_URL}")
        await page.goto(WEBSITE_URL)

        # 登录（根据您网站的登录表单自定义此部分）
        try:
            await login(page, USERNAME, PASSWORD)
            print("登录成功")
        except Exception as e:
            print(f"登录失败: {e}")
            await browser.close()
            return

        # 处理每个订单
        for order_code, order_data in order_groups:
            print(f"\n正在处理订单: {order_code}")

            # 导航到订单创建页面（根据您的网站自定义）
            first_row = order_data.iloc[0]
            await navigate_to_order_creation(page,  first_row)

            # 填写基本订单信息（第一行包含订单头信息）
            # first_row = order_data.iloc[0]
            # await fill_order_header(page, first_row)

            # 添加此订单的所有商品
            for _, product_row in order_data.iterrows():
                await add_product(page, product_row)

            # 提交订单并获取订单号
            order_number = await submit_order(page)

            if order_number:
                print(f"订单创建成功。订单号: {order_number}")

                # 用新订单号更新此订单编码的所有行
                df.loc[df['订单编码'] == order_code, '订单编号'] = order_number
            else:
                print("获取订单号失败")

        # 保存更新后的Excel文件
        output_file = f"updated_{os.path.basename(EXCEL_FILE_PATH)}"
        df.to_excel(output_file, index=False)
        print(f"\n所有订单处理完成。更新后的Excel已保存到: {output_file}")

        await browser.close()

async def login(page, username, password):
    """处理网站登录。根据您网站的登录表单自定义此函数。"""
    # 登录表单交互示例 - 根据您的网站更新选择器
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Enter")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")
    page.get_by_role("button", name="添 加").click()

    # # 等待登录完成 - 根据您的网站调整选择器
    # await page.wait_for_selector('.dashboard', timeout=10000)

async def navigate_to_order_creation(page,order_data):
    """导航到订单创建页面。根据您的网站自定义。"""
    # 导航示例 - 根据您的网站更新选择器

    page.locator(".el-input__inner").first.click()
    page.locator(".el-input__inner").first.fill(order_data["项目"])
    page.get_by_text(order_data["项目"]).click()
    page.locator("div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
    page.get_by_text(order_data["客户"]).click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_placeholder("请输入内容").fill(order_data['收货人'])
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(order_data['收货电话'])

    ###选择省市区
    page.locator(".basic_mess > div:nth-child(2) > div:nth-child(3) > div").click()
    print("✅ 已点击地址选择框")
    time.sleep(1)

    # **选择省份**
    page.get_by_placeholder("省/直辖市").click()
    time.sleep(1)
    page.get_by_text(order_data['省/直辖市'], exact=True).click()
    # print(f"✅ 已选择省份: {province}")
    time.sleep(2)  # 确保城市列表刷新

    # **判断是否为直辖市**
    # is_municipality = province in ["北京市", "上海市", "天津市", "重庆市"]
    is_municipality = order_data['省/直辖市'] in ["北京市", "上海市", "天津市"]

    # **点击城市输入框**
    page.get_by_placeholder("城市").click()
    time.sleep(1)

    if is_municipality:
        print(f"⚠️ 直辖市特殊处理: 选择 {order_data['省/直辖市']} 作为城市")
        city_option = page.locator("ul").filter(has_text=re.compile(rf"^{order_data['省/直辖市']}$")).locator("span")
    else:
        city_option = page.get_by_text(order_data['城市'], exact=True)

    # **等待城市选项加载并选择**
    #expect(city_option).to_be_visible(timeout=5000)
    city_option.scroll_into_view_if_needed()
    city_option.click()
    # print(f"✅ 已选择城市: {city if not is_municipality else province}")
    time.sleep(2)  # **确保区/县数据刷新**

    # **选择区/县**
    page.get_by_placeholder("区/县").click()
    time.sleep(1)
    page.get_by_text(order_data['区/县'], exact=True).click()
    #print(f"✅ 已选择区/县: {order_data['区/县']}")
    time.sleep(1)
    page.get_by_placeholder("请输入200字以内").click()
    page.get_by_placeholder("请输入200字以内").fill(order_data['详细地址'])
    page.get_by_role("button", name="确 定").click()

async def fill_order_header(page, order_data):
    """填写基本订单信息。根据您网站的表单自定义。"""
    # 表单填写示例 - 根据您的网站更新选择器和字段
    await page.fill('input[name="project"]', str(order_data['项目']))
    await page.fill('input[name="customer"]', str(order_data['客户']))
    await page.fill('input[name="recipient"]', str(order_data['收货人']))
    await page.fill('input[name="phone"]', str(order_data['收货电话']))

    # 处理地址字段
    await page.select_option('select[name="province"]', value=str(order_data['省/直辖市']))
    await page.select_option('select[name="city"]', value=str(order_data['城市']))
    await page.select_option('select[name="district"]', value=str(order_data['区/县']))
    await page.fill('textarea[name="address"]', str(order_data['详细地址']))

    # 业务信息
    await page.fill('input[name="salesperson"]', str(order_data['业务员']))
    await page.fill('input[name="business_rep"]', str(order_data['商务员']))
    await page.select_option('select[name="business_type"]', value=str(order_data['业务类型']))

async def add_product(page, product_data):
    """向订单添加商品。根据您网站的表单自定义。"""
    # 添加商品示例 - 根据您的网站更新选择器
    await page.click('button.add-product')

    # 等待商品表单出现
    await page.wait_for_selector('.product-form-row:last-child')

    # 在最后一行商品行中填写商品详情
    last_row = '.product-form-row:last-child'
    await page.fill(f'{last_row} input[name="product_code"]', str(product_data['商品编号']))
    await page.fill(f'{last_row} input[name="quantity"]', str(product_data['商品数量']))
    await page.fill(f'{last_row} input[name="price"]', str(product_data['售价']))

async def submit_order(page):
    """提交订单并提取订单号。根据您的网站自定义。"""
    # 点击保存/提交按钮
    await page.click('button[type="submit"]')

    # 等待带有订单号的确认消息
    try:
        await page.wait_for_selector('.order-confirmation', timeout=20000)

        # 提取订单号 - 根据订单号出现的位置更新选择器
        order_number_element = await page.query_selector('.order-number')
        if order_number_element:
            order_number = await order_number_element.inner_text()
            # 清理订单号（删除任何类似"订单号："的文本）
            order_number = order_number.replace("订单号：", "").strip()
            return order_number
    except Exception as e:
        print(f"提交订单时出错: {e}")

    return None

if __name__ == "__main__":
    asyncio.run(main())
