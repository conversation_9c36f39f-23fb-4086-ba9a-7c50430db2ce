# encoding: utf-8
# @File  : order_automation.py
# @Desc  : Automate order creation and update Excel with order numbers

import pandas as pd
import asyncio
from playwright.async_api import async_playwright
import time
import os

# Configuration - Update these values
EXCEL_FILE_PATH = './订单数据.xlsx'  # Path to your Excel file
WEBSITE_URL = 'https://your-company-website.com'  # Replace with your company's website URL
USERNAME = 'your_username'  # Replace with your login username
PASSWORD = 'your_password'  # Replace with your login password

async def main():
    # Read the Excel file
    print(f"Reading Excel file: {EXCEL_FILE_PATH}")
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        print(f"Successfully read Excel file with {len(df)} rows")
        print("Columns in Excel:", df.columns.tolist())
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return

    # Check if required columns exist
    required_columns = ['订单编码', '项目', '客户', '收货人', '收货电话', '省/直辖市', '城市', 
                        '区/县', '详细地址', '业务员', '商务员', '业务类型', 
                        '商品编号', '商品数量', '售价']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Missing required columns: {missing_columns}")
        return

    # Make sure '订单编号' column exists, add it if not
    if '订单编号' not in df.columns:
        df['订单编号'] = ''
        print("Added '订单编号' column to the dataframe")

    # Group by order code
    order_groups = df.groupby('订单编码')
    print(f"Found {len(order_groups)} unique orders to process")

    # Initialize Playwright
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Set headless=True for production
        context = await browser.new_context()
        page = await context.new_page()

        # Navigate to the website
        print(f"Navigating to {WEBSITE_URL}")
        await page.goto(WEBSITE_URL)
        
        # Login (customize this section based on your website's login form)
        try:
            await login(page, USERNAME, PASSWORD)
            print("Login successful")
        except Exception as e:
            print(f"Login failed: {e}")
            await browser.close()
            return

        # Process each order
        for order_code, order_data in order_groups:
            print(f"\nProcessing order: {order_code}")
            
            # Navigate to order creation page (customize this based on your website)
            await navigate_to_order_creation(page)
            
            # Fill basic order information (first row contains the order header info)
            first_row = order_data.iloc[0]
            await fill_order_header(page, first_row)
            
            # Add all products for this order
            for _, product_row in order_data.iterrows():
                await add_product(page, product_row)
            
            # Submit the order and get the order number
            order_number = await submit_order(page)
            
            if order_number:
                print(f"Order created successfully. Order number: {order_number}")
                
                # Update all rows for this order code with the new order number
                df.loc[df['订单编码'] == order_code, '订单编号'] = order_number
            else:
                print("Failed to get order number")
        
        # Save the updated Excel file
        output_file = f"updated_{os.path.basename(EXCEL_FILE_PATH)}"
        df.to_excel(output_file, index=False)
        print(f"\nAll orders processed. Updated Excel saved to: {output_file}")
        
        await browser.close()

async def login(page, username, password):
    """Handle login to the website. Customize this based on your website's login form."""
    # Example login form interaction - update selectors based on your website
    await page.fill('input[name="username"]', username)
    await page.fill('input[name="password"]', password)
    await page.click('button[type="submit"]')
    
    # Wait for login to complete - adjust selector based on your website
    await page.wait_for_selector('.dashboard', timeout=10000)

async def navigate_to_order_creation(page):
    """Navigate to the order creation page. Customize based on your website."""
    # Example navigation - update selectors based on your website
    await page.click('a.create-order-link')
    await page.wait_for_selector('form.order-form', timeout=10000)

async def fill_order_header(page, order_data):
    """Fill in the basic order information. Customize based on your website's form."""
    # Example form filling - update selectors and fields based on your website
    await page.fill('input[name="project"]', str(order_data['项目']))
    await page.fill('input[name="customer"]', str(order_data['客户']))
    await page.fill('input[name="recipient"]', str(order_data['收货人']))
    await page.fill('input[name="phone"]', str(order_data['收货电话']))
    
    # Handle address fields
    await page.select_option('select[name="province"]', value=str(order_data['省/直辖市']))
    await page.select_option('select[name="city"]', value=str(order_data['城市']))
    await page.select_option('select[name="district"]', value=str(order_data['区/县']))
    await page.fill('textarea[name="address"]', str(order_data['详细地址']))
    
    # Business information
    await page.fill('input[name="salesperson"]', str(order_data['业务员']))
    await page.fill('input[name="business_rep"]', str(order_data['商务员']))
    await page.select_option('select[name="business_type"]', value=str(order_data['业务类型']))

async def add_product(page, product_data):
    """Add a product to the order. Customize based on your website's form."""
    # Example product addition - update selectors based on your website
    await page.click('button.add-product')
    
    # Wait for product form to appear
    await page.wait_for_selector('.product-form-row:last-child')
    
    # Fill product details in the last product row
    last_row = '.product-form-row:last-child'
    await page.fill(f'{last_row} input[name="product_code"]', str(product_data['商品编号']))
    await page.fill(f'{last_row} input[name="quantity"]', str(product_data['商品数量']))
    await page.fill(f'{last_row} input[name="price"]', str(product_data['售价']))

async def submit_order(page):
    """Submit the order and extract the order number. Customize based on your website."""
    # Click save/submit button
    await page.click('button[type="submit"]')
    
    # Wait for confirmation message with order number
    try:
        await page.wait_for_selector('.order-confirmation', timeout=20000)
        
        # Extract order number - update selector based on where the order number appears
        order_number_element = await page.query_selector('.order-number')
        if order_number_element:
            order_number = await order_number_element.inner_text()
            # Clean up the order number (remove any text like "Order Number: ")
            order_number = order_number.replace("Order Number: ", "").strip()
            return order_number
    except Exception as e:
        print(f"Error submitting order: {e}")
    
    return None

if __name__ == "__main__":
    asyncio.run(main())
