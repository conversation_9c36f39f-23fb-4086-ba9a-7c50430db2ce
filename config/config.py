import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent

class Config:
    BROWSER = os.getenv('BROWSER', 'chromium')
    HEADLESS = os.getenv('HEADLESS', 'True').lower() == 'true'
    BASE_URL = os.getenv('BASE_URL', 'https://example.com')
    ALLURE_DIR = BASE_DIR / 'allure-results'
    SCREENSHOT_DIR = BASE_DIR / 'screenshots'
    DINGTALK_WEBHOOK = os.getenv('DINGTALK_WEBHOOK', '')