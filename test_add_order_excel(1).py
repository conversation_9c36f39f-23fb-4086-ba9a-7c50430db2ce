# """
# @File        : test_add_order_excel.py
# <AUTHOR> yangxue
# @Date        : 2025-05-13
# @Description : 支持 Excel 驱动的新增接单--发货脚本
# """
# import re
# import time
# from datetime import datetime
# import allure
# import openpyxl
# import pytest
# import logging
# from common.attach import screenshot_after_step
# from common.excel_reader import ensure_order_column
# from common.ui_action import select_project, select_address
# logger = logging.getLogger(__name__)
# pytestmark = [
#     pytest.mark.order(3),
#     pytest.mark.use_excel,  # ✅ 启用 Excel 驱动标记
#     pytest.mark.warehouse(enable=False, disable=True),
# ]
# @allure.feature("订单管理")
# @allure.story("新增接单")
# def test_add_order_excel(test_data_excel, get_mark_account, login_web):
#     # set_order_no, _ = order_no_storage
#     page = login_web
#     # ✅ 打开 Excel 文件准备写回订单编号
#     excel_file_path = "E:/leading_erpautoui/data/order_data.xlsx"
#     wb = openpyxl.load_workbook(excel_file_path)
#     sheet = wb.active
#     write_column_index = ensure_order_column(sheet)
#     failed_rows = []  # ✅ 新增：缓存失败记录
#     with allure.step("进入新增接单页面"):
#         # ✅ 每轮都回首页重新点菜单，保证环境干净
#         page.get_by_role("menuitem", name="首页").click()
#         page.get_by_role("menuitem", name="订单").click()
#         page.get_by_text("新增接单", exact=True).first.click()
#         page.wait_for_selector("text=项目", timeout=5000)
#         screenshot_after_step(page, description="进入新增接单页面")
#     for case in test_data_excel:
#         row_index = case.get("row_index", -1)
#         excel_row_index = row_index + 2 if row_index >= 0 else None
#         # ✅ 如果已有订单编号且不是失败记录，跳过该条
#         existing_value = sheet.cell(row=excel_row_index, column=write_column_index).value if excel_row_index else None
#         if existing_value and not str(existing_value).startswith("失败"):
#             logger.info(f"✅ 跳过已创建订单（第 {excel_row_index} 行）: {existing_value}")
#             continue
#         try:
#             input_data = case["input"]
#             logger.info(f"\n\n📦 开始执行: {case['description']}")
#             # **填写基本信息**
#             with allure.step("填写基本信息"):
#                 select_project(page, input_data["project"])
#                 page.locator(
#                     "div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
#                 page.get_by_text(input_data["customer"]).click()
#                 page.get_by_placeholder("请输入内容").click()
#                 page.get_by_placeholder("请输入内容").fill(input_data["contact"]["name"])
#                 page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
#                 page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(
#                     input_data["contact"]["phone"])
#                 select_address(page, input_data["address"]["province"], input_data["address"]["city"], input_data["address"]["district"])
#                 page.get_by_placeholder("请输入200字以内").click()
#                 page.get_by_placeholder("请输入200字以内").fill(input_data["address"]["detail"])
#                 page.get_by_role("button", name="确 定").click()
#                 screenshot_after_step(page, description="填写基本信息")
#             # **填写商务员**
#             with allure.step("填写商务员"):
#                 page.locator(
#                     "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
#                 page.locator(
#                     "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
#                     input_data["business_staff"]["primary"])
#                 time.sleep(1)
#                 page.get_by_text(input_data["business_staff"]["primary"]).click()
#                 # page.locator(
#                 #     "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
#                 #     "杨雪")
#
#                 # page.get_by_text(input_data["business_staff"]["primary"]).click()
#
#                 page.locator(
#                     "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
#                 page.locator(
#                     "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
#                     input_data["business_staff"]["secondary"])
#                 time.sleep(1)
#                 page.get_by_text(input_data["business_staff"]["secondary"]).nth(1).click()
#                 # page.locator(
#                 #     "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
#                 #     "杨雪")
#                 # page.get_by_text(input_data["business_staff"]["secondary"]).nth(1).click()
#
#                 page.get_by_placeholder("选择日期").click()
#                 page.locator("span:nth-child(3) > .el-input__suffix-inner > .el-input__icon").click()
#
#             with allure.step("填写商品信息"):
#                 product = input_data["products"][0]
#                 # 填写商品编码
#                 page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).click()
#                 page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).fill(product["sku"])
#                 page.get_by_text(product["sku"]).nth(3).click()
#                 # 填写单价
#                 page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).click()
#                 page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).press(
#                     "ControlOrMeta+a")
#                 page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).fill(
#                     str(product["price"]))
#                 # 填写数量
#                 page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).click()
#                 page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).fill(
#                     str(product["quantity"]))
#                 # 保存订单
#             with allure.step("保存订单"):
#                 page.get_by_role("button", name="保存订单").click()
#                 page.wait_for_selector("#inviteCode", timeout=5000)
#                 order_no = page.locator("#inviteCode").inner_text().strip()
#                 page.get_by_role("button", name="确 定").click()
#                 sheet.cell(row=excel_row_index, column=write_column_index).value = order_no
#
#         except Exception as e:
#             error_text = f"失败: {str(e)}"
#             sheet.cell(row=excel_row_index, column=write_column_index).value = error_text
#             failed_rows.append([cell.value for cell in sheet[excel_row_index]])
#
#         finally:
#             wb.save(excel_file_path)
#     # 导出失败数据
#     if failed_rows:
#         from openpyxl import Workbook
#         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#         failed_path = f"E:/leading_erpautoui/data/order_failed_{timestamp}.xlsx"
#         wb_failed = Workbook()
#         ws = wb_failed.active
#         ws.append([cell.value for cell in sheet[1]])  # 表头
#         for row in failed_rows:
#             ws.append(row)
#             wb_failed.save(failed_path)
#             logger.warning(f"⚠ 所有失败订单已统一导出至: {failed_path}")
#     else:
#         logger.info("✅ 所有订单执行成功，无失败记录导出")
#
#
#
#
#
#
import re
import time
from datetime import datetime
import allure
import openpyxl
import pytest
import logging
from common.attach import screenshot_after_step
from common.excel_reader import ensure_order_column
from common.ui_action import select_project, select_address

logger = logging.getLogger(__name__)

pytestmark = [
    pytest.mark.order(3),
    pytest.mark.use_excel,
    pytest.mark.warehouse(enable=False, disable=True),
]


@allure.feature("订单管理")
@allure.story("新增接单")
def test_add_order_excel(test_data_excel, get_mark_account, login_web):
    page = login_web
    excel_file_path = "/data/order_data3.xlsx"
    wb = openpyxl.load_workbook(excel_file_path)
    sheet = wb.active
    write_column_index = ensure_order_column(sheet)
    failed_rows = []

    def reset_to_home():
        """异常后重置页面状态"""
        try:
            page.get_by_role("menuitem", name="首页").click(timeout=3000)
            page.get_by_role("menuitem", name="订单").click(timeout=3000)
            page.get_by_text("新增接单", exact=True).first.click(timeout=3000)
            page.wait_for_selector("text=项目", timeout=5000)
            logger.info("✅ 页面已重置回新增接单页")
        except Exception as reset_err:
            logger.warning(f"⚠️ 页面重置失败: {reset_err}")

    with allure.step("进入新增接单页面"):
        reset_to_home()
        screenshot_after_step(page, description="进入新增接单页面")

    for idx, case in enumerate(test_data_excel):
        row_index = case.get("row_index", -1)
        excel_row_index = row_index + 2 if row_index >= 0 else None
        input_data = case["input"]
        description = case.get("description", f"第 {excel_row_index} 行")

        logger.info(f"\n\n🚀 开始执行第 {idx + 1} 条数据：{description}")
        existing_value = sheet.cell(row=excel_row_index, column=write_column_index).value if excel_row_index else None
        if existing_value and not str(existing_value).startswith("失败"):
            logger.info(f"✅ 跳过已创建订单（第 {excel_row_index} 行）: {existing_value}")
            continue

        try:
            with allure.step("填写基本信息"):
                select_project(page, input_data["project"])
                page.locator(
                    "div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
                page.get_by_text(input_data["customer"]).click()
                page.get_by_placeholder("请输入内容").click()
                page.get_by_placeholder("请输入内容").fill(input_data["contact"]["name"])
                page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
                page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(
                    input_data["contact"]["phone"])
                select_address(page, input_data["address"]["province"], input_data["address"]["city"],
                               input_data["address"]["district"])
                page.get_by_placeholder("请输入200字以内").click()
                page.get_by_placeholder("请输入200字以内").fill(input_data["address"]["detail"])
                page.get_by_role("button", name="确 定").click()
                screenshot_after_step(page, description="填写基本信息")

            with allure.step("填写商务员"):
                page.locator(
                    "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
                page.locator(
                    "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
                    input_data["business_staff"]["primary"])
                time.sleep(1)
                page.get_by_text(input_data["business_staff"]["primary"]).click()

                page.locator(
                    "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
                page.locator(
                    "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
                    input_data["business_staff"]["secondary"])
                time.sleep(1)
                page.get_by_text(input_data["business_staff"]["secondary"]).click()
                page.get_by_placeholder("选择日期").click()
                page.locator("span:nth-child(3) > .el-input__suffix-inner > .el-input__icon").click()

            with allure.step("填写商品信息"):
                product = input_data["products"][0]
                page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).click()
                page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).fill(product["sku"])
                page.get_by_text(product["sku"]).nth(3).click()
                page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).click()
                page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).press("ControlOrMeta+a")
                page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).fill(str(product["price"]))
                page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).click()
                page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).fill(str(product["quantity"]))

            with allure.step("保存订单"):
                page.get_by_role("button", name="保存订单").click()
                page.wait_for_selector("#inviteCode", timeout=5000)
                order_no = page.locator("#inviteCode").inner_text().strip()
                page.get_by_role("button", name="确 定").click()
                sheet.cell(row=excel_row_index, column=write_column_index).value = order_no
                logger.info(f"✅ 成功创建订单: {order_no}")

        except Exception as e:
            error_text = f"失败_{datetime.now().strftime('%H%M%S')}: {str(e)}"
            sheet.cell(row=excel_row_index, column=write_column_index).value = error_text
            failed_rows.append([cell.value for cell in sheet[excel_row_index]])
            screenshot_after_step(page, description=f"❌ 创建失败截图 - 第{excel_row_index}行")
            logger.error(f"❌ 第 {excel_row_index} 行订单创建失败: {str(e)}")
            reset_to_home()

        finally:
            wb.save(excel_file_path)

    # 导出失败记录
    if failed_rows:
        from openpyxl import Workbook
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        failed_path = f"E:/leading_erpautoui/data/order_failed_{timestamp}.xlsx"
        wb_failed = Workbook()
        ws = wb_failed.active
        ws.append([cell.value for cell in sheet[1]])  # 表头
        for row in failed_rows:
            ws.append(row)
        wb_failed.save(failed_path)
        logger.warning(f"⚠ 所有失败订单已统一导出至: {failed_path}")
    else:
        logger.info("✅ 所有订单执行成功，无失败记录导出")
