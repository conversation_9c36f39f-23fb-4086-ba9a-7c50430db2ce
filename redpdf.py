# encoding: utf-8
# @File  : redpdf.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/04/29 17:22

import PyPDF2
import fitz  # PyMuPDF
from PIL import Image

def read_pdf(file_path):
    """
    读取PDF文件并提取页面信息
    :param file_path: PDF文件路径
    :return: 返回PDF文件的所有页面内容
    """
    content = ""
    try:
        # 使用PyMuPDF提取文本
        doc = fitz.open(file_path)
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            try:
                # 尝试提取文本，如果遇到错误则跳过该页
                content += page.get_text()
            except Exception as e:
                print(f"Error extracting text from page {page_num + 1}: {e}")
                continue
    except Exception as e:
        print(f"Error opening PDF file: {e}")
    return content

def convert_pdf_to_images(pdf_path):
    """
    将PDF文件的每一页转换为JPG图片并保存到当前目录
    :param pdf_path: PDF文件路径
    """
    import os
    
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    # 获取PDF文件所在目录
    output_dir = os.path.dirname(pdf_path)
    
    # 遍历每一页
    for page_num in range(len(doc)):
        # 获取当前页
        page = doc.load_page(page_num)
        
        # 将页面转换为图片，确保参数为整数
        pix = page.get_pixmap(matrix=fitz.Matrix(1.0, 1.0))  # 修改为浮点数类型
        
        # 创建Image对象
        image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        
        # 构建保存路径
        output_path = os.path.join(output_dir, f"page_{page_num + 1}.jpg")
        
        # 保存图片为JPG格式
        image.save(output_path, "JPEG")
        print(f"Page {page_num + 1} has been converted and saved as {output_path}")

if __name__ == "__main__":
    path = r'G:\paoshuju\pdf转图片\签收单-14147-20240914.pdf'
    
    # 读取PDF内容
    pdf_content = read_pdf(path)
    print(pdf_content)
    
    # 转换PDF为图片
    convert_pdf_to_images(path)