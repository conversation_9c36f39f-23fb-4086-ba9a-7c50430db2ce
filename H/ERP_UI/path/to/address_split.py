
def parse_address(address):
    # 调整正则表达式以正确匹配区的部分
    pattern = r'^(?P<province>[^省]+省)?(?P<city>[^市]+市)?(?P<district>[^县区]+县|[^区]+区|[^市]+市)(?P<detail>.*)$'
    match = re.match(pattern, address)
    
    if match:
        result = match.groupdict()
        
        # 修正区值处理逻辑，确保只包含正确的区名
        result['district'] = result['district'].replace('县', '').replace('区', '').replace('市', '')  # 修改: 确保区值仅为正确的区名
        result['detail'] = address.replace(result['province'], '').replace(result['city'], '').replace(result['district'], '').strip()  # 修改: 确保详细地址部分正确
        
        return result
    return None
