import tkinter as tk
from tkinter import messagebox, simpledialog, filedialog
from tkinter import ttk  # 添加此行导入 ttk 模块


class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("京东价格监控")
        self.geometry("800x600")

        # 创建输入框和标签
        tk.Label(self, text="品牌名称:").grid(row=0, column=0, padx=10, pady=10, sticky="e")
        self.brand_name_entry = tk.Entry(self)
        self.brand_name_entry.grid(row=0, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="型号:").grid(row=1, column=0, padx=10, pady=10, sticky="e")
        self.model_entry = tk.Entry(self)
        self.model_entry.grid(row=1, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="产品名称:").grid(row=2, column=0, padx=10, pady=10, sticky="e")
        self.product_name_entry = tk.Entry(self)
        self.product_name_entry.grid(row=2, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="单位:").grid(row=3, column=0, padx=10, pady=10, sticky="e")
        self.unit_entry = tk.Entry(self)
        self.unit_entry.grid(row=3, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="京东链接:").grid(row=4, column=0, padx=10, pady=10, sticky="e")
        self.jd_url_entry = tk.Entry(self)
        self.jd_url_entry.grid(row=4, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="采购价格:").grid(row=5, column=0, padx=10, pady=10, sticky="e")
        self.purchase_price_entry = tk.Entry(self)
        self.purchase_price_entry.grid(row=5, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="网站价格:").grid(row=6, column=0, padx=10, pady=10, sticky="e")
        self.website_price_entry = tk.Entry(self)
        self.website_price_entry.grid(row=6, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="市场价:").grid(row=7, column=0, padx=10, pady=10, sticky="e")
        self.market_price_entry = tk.Entry(self)
        self.market_price_entry.grid(row=7, column=1, padx=10, pady=10, sticky="w")

        tk.Label(self, text="执行结果:").grid(row=8, column=0, padx=10, pady=10, sticky="e")
        self.res_entry = tk.Entry(self)
        self.res_entry.grid(row=8, column=1, padx=10, pady=10, sticky="w")

        # 创建 Treeview 组件并设置高度为20行
        self.tree = ttk.Treeview(result_frame, columns=("ser_no", "brand_name", "model", "product_name", "unit", "jd_url", "purchase_price", "website_price", "market_price", "res"), show="headings", height=20)  # 设置 Treeview 高度为20行
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 设置列标题及宽度
        self.tree.heading("ser_no", text="序号")
        self.tree.column("ser_no", width=50)
        self.tree.heading("brand_name", text="品牌名称")
        self.tree.column("brand_name", width=100)  # 增加品牌名称列宽度
        self.tree.heading("model", text="型号")
        self.tree.column("model", width=80)
        self.tree.heading("product_name", text="产品名称")
        self.tree.column("product_name", width=150)
        self.tree.heading("unit", text="单位")
        self.tree.column("unit", width=60)
        self.tree.heading("jd_url", text="京东链接")
        self.tree.column("jd_url", width=150)
        self.tree.heading("purchase_price", text="采购价格")
        self.tree.column("purchase_price", width=80)
        self.tree.heading("website_price", text="网站价格")
        self.tree.column("website_price", width=80)
        self.tree.heading("market_price", text="市场价")
        self.tree.column("market_price", width=80)
        self.tree.heading("res", text="执行结果")
        self.tree.column("res", width=60)

        # 添加垂直滚动条
        scrollbar_y = tk.Scrollbar(result_frame, command=self.tree.yview)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加水平滚动条
        scrollbar_x = tk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 配置 Treeview 和滚动条
        self.tree.config(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 导出按钮放置在 Treeview 下方
        export_frame = tk.Frame(self, bg="white", padx=20, pady=20)
        export_frame.grid(row=7, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")
        self.export_button = tk.Button(export_frame, text="导出执行结果", command=self.export_data, bg="#4CAF50", fg="white", font=("Arial", 10))
        self.export_button.pack(pady=10)

        # 设置表头背景色和滚动条样式
        style = ttk.Style()
        style.configure("Treeview.Heading", background="#4CAF50", foreground="white", font=("Arial", 10))

        style.configure("Vertical.TScrollbar", troughcolor="lightgray", gripcount=0,
                        background="#4CAF50", darkcolor="gray", lightcolor="gray",
                        troughrelief="flat", borderwidth=1, arrowcolor="white")

        style.configure("Horizontal.TScrollbar", troughcolor="lightgray", gripcount=0,
                        background="#4CAF50", darkcolor="gray", lightcolor="gray",
                        troughrelief="flat", borderwidth=1, arrowcolor="white")

    def export_data(self):
        data = self.tree.get_children()
        if not data:
            messagebox.showinfo("提示", "没有数据可以导出")
            return

        file_path = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV files", "*.csv")])
        if file_path:
            with open(file_path, "w", newline="") as file:
                for item in data:
                    values = self.tree.item(item, "values")
                    file.write(",".join(values) + "\n")
            messagebox.showinfo("提示", "数据已成功导出到 {}".format(file_path))


if __name__ == "__main__":
    app = App()
    app.mainloop()
