import time

from playwright.sync_api import Playwright, sync_playwright, expect

import  pandas as pd
def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width": 1280, "height": 950})
    page = context.new_page()

    try:
        page.goto("https://lxerptest2.66123123.com/")


        page.get_by_placeholder("请输入账号").click()
        page.get_by_placeholder("请输入账号").fill("liujq")
        page.get_by_placeholder("请输入账号").press("Enter")
        page.get_by_placeholder("请输入账号").fill("liujianqiang")
        page.get_by_placeholder("请输入账号").press("Tab")
        page.get_by_placeholder("请输入密码").fill("a111111")
        page.get_by_placeholder("请输入密码").press("Enter")

        page.get_by_role("menuitem", name="订单").click()
        # page.get_by_text("派单报备").click()
        # page.get_by_label("接单中心").get_by_text("接单管理").click()

        res = {}
        orderDeliveryNoList = []
        # 注册响应监听器
        def handle_response(response):
            if response.ok and 'application/json' in response.headers.get('content-type', ''):
                try:
                    json_data = response.json()
                    print(response.url+":"+str(json_data))
                    if isinstance(json_data['data'], dict) and 'associate-list' in response.url:
                        print(json_data['data'])
                        # orderDeliveryNoList=[]
                        for  item in json_data['data']['items']:
                            if isinstance(item, dict):
                                orderDeliveryNoList.append(item['orderDeliveryNo'])
                                #res['发货单号']=item['orderDeliveryNo']

                        # orderDeliveryNoList.append(res)
                        print('orderDeliveryNoList：'+','.join(orderDeliveryNoList))
                except Exception as e:
                    print(f"json格式解析失败 {response.url}: {e}")
            else:
               print(f"响应信息无法转换json {response.url}: {response.status} {response.status_text}")

        page.on("response", handle_response)
        page.get_by_text("发货单管理", exact=True).click()
        page.wait_for_timeout(1000)
        numb = page.locator("//li[contains(@class, 'number')]").count()
        print(numb)
        # num = 57
        # page = num % 10
        # print(num // 10)
        # if page == 0:
        #     page = num // 10
        #     print(page)
        # else:
        #     page = (num // 10) + 1
        #     print(page)
        for i in range(1, numb+1):
            # page.get_by_role("menuitem", name="订单").click()
            print('点击当前页:' + str(i))
            page.get_by_text(str(i), exact=True).click()
        page.wait_for_timeout(1000)

        page.remove_listener("response", handle_response)

        res[f'发货单号1-{numb}页数据']=orderDeliveryNoList
        print(res)
        df = pd.DataFrame(res)
        df.to_excel("test.xlsx",index=False)

    except Exception as e:
        print(f"元素点击失败: {e}")

    finally:
        context.close()
        browser.close()


with sync_playwright() as playwright:
    run(playwright)
