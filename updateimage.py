# from fastapi import FastAPI, Query
#
# app = FastAPI()
#
# @app.get("/items/")
# async def read_items(q: str = Query(None, min_length=3, max_length=50)):
#     return {"q": q}




# from pydantic import BaseModel
# from fastapi import FastAPI
#
# app = FastAPI()
#
# class Item(BaseModel):
#     name: str
#     description: str = None
#     price: float
#     tax: float = None
#
# @app.post("/items/")
# async def create_item(item: Item):
#     return item




from fastapi import FastAPI, Request

app = FastAPI()

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Custom-Header"] = "Custom Value"
    return response

@app.get("/")
async def root():
    return {"message": "Hello World"}


if __name__ == '__main__':
    list=[{'id': '559AD1349E901E71', 'name': 'P2022112509325054855901配送单.pdf'}]
    import json
    str=json.dumps(list)
    str1=str.replace('\'', '\"')
    print(str1)
    list2=json.loads(str1)
    print(list2)






