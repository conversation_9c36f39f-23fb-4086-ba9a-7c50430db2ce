# encoding: utf-8
# @File  : 订单数据_模板.py
# @Desc  : Create a template Excel file for order automation

import pandas as pd
import numpy as np

# Create a sample dataframe with the required columns
data = {
    '订单编码': ['ORD001', 'ORD001', 'ORD001', 'ORD002', 'ORD002'],
    '项目': ['项目A', '项目A', '项目A', '项目B', '项目B'],
    '客户': ['客户公司A', '客户公司A', '客户公司A', '客户公司B', '客户公司B'],
    '收货人': ['张三', '张三', '张三', '李四', '李四'],
    '收货电话': ['13800138000', '13800138000', '13800138000', '13900139000', '13900139000'],
    '省/直辖市': ['上海市', '上海市', '上海市', '北京市', '北京市'],
    '城市': ['上海市', '上海市', '上海市', '北京市', '北京市'],
    '区/县': ['浦东新区', '浦东新区', '浦东新区', '朝阳区', '朝阳区'],
    '详细地址': ['XX路XX号', 'XX路XX号', 'XX路XX号', 'YY路YY号', 'YY路YY号'],
    '业务员': ['王业务', '王业务', '王业务', '赵业务', '赵业务'],
    '商务员': ['钱商务', '钱商务', '钱商务', '孙商务', '孙商务'],
    '业务类型': ['常规', '常规', '常规', '常规', '常规'],
    '商品编号': ['10066659', '12239562', '12178553', '10583090', '10150278'],
    '商品数量': [10, 1, 2, 50, 15],
    '售价': [16.00, 2078.00, 30.00, 5.33, 12.00],
    '订单编号': ['', '', '', '', '']
}

# Create DataFrame
df = pd.DataFrame(data)

# Save to Excel
df.to_excel('订单数据_模板.xlsx', index=False)
print("Template Excel file created: 订单数据_模板.xlsx")

# Display sample data
print("\nSample data:")
print(df.head())
