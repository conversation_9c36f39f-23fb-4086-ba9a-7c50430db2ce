# encoding: utf-8
# @File  : install_dependencies.py
# @Desc  : Install required dependencies for order automation script

import subprocess
import sys
import os

def check_package_installed(package_name):
    try:
        __import__(package_name)
        print(f"✓ {package_name} is already installed")
        return True
    except ImportError:
        print(f"✗ {package_name} is not installed")
        return False

def install_package(package_name):
    print(f"Installing {package_name}...")
    try:
        # Try to install the package
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package_name}: {e}")
        return False

def main():
    print("Checking required packages for order automation script...")
    
    # List of required packages
    required_packages = [
        "pandas",
        "numpy",
        "playwright",
        "asyncio"
    ]
    
    # Check and install missing packages
    missing_packages = []
    for package in required_packages:
        if not check_package_installed(package):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nNeed to install {len(missing_packages)} package(s): {', '.join(missing_packages)}")
        
        # Try to install missing packages
        failed_packages = []
        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\nFailed to install the following packages: {', '.join(failed_packages)}")
            print("\nPlease try to install them manually with:")
            for package in failed_packages:
                print(f"pip install {package}")
            print("\nOr download the wheel files and install them offline with:")
            print("pip install path/to/package.whl")
            return False
    else:
        print("\nAll required packages are already installed!")
    
    print("\nYou can now run the order automation script with:")
    print("python order_automation_multithread.py")
    return True

if __name__ == "__main__":
    main()