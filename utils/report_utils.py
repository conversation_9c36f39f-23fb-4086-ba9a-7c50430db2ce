import allure
import requests
import json
from config.config import Config

def attach_screenshot(page, name):
    screenshot = page.screenshot()
    allure.attach(screenshot, name, allure.attachment_type.PNG)

def send_dingtalk_notification(result):
    if not Config.DINGTALK_WEBHOOK:
        return
        
    message = {
        "msgtype": "markdown",
        "markdown": {
            "title": "测试结果通知",
            "text": f"### 测试结果\n\n**总用例数**: {result['total']}\n\n**通过数**: {result['passed']}\n\n**失败数**: {result['failed']}"
        }
    }
    
    requests.post(Config.DINGTALK_WEBHOOK, json=message)