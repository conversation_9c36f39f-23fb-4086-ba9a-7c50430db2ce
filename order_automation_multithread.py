# encoding: utf-8
# @File  : order_automation_multithread.py
# @Desc  : 多线程自动创建订单并将订单号更新到Excel文件，支持断点续传和错误恢复

import pandas as pd
import asyncio
import time
import os
import json
import re
import logging
import concurrent.futures
from datetime import datetime
from playwright.async_api import async_playwright
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("order_automation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置信息 - 请更新以下值
EXCEL_FILE_PATH = './订单数据.xlsx'  # Excel文件路径
WEBSITE_URL = 'https://your-company-website.com'  # 替换为您公司的网站URL
USERNAME = 'your_username'  # 替换为您的登录用户名
PASSWORD = 'your_password'  # 替换为您的登录密码
MAX_WORKERS = 3  # 最大并行处理订单数
MAX_RETRIES = 3  # 失败时最大重试次数
PROGRESS_FILE = 'order_progress.json'  # 进度保存文件
SAVE_INTERVAL = 2  # 每处理多少个订单保存一次进度

# 保存进度
def save_progress(completed_orders, failed_orders):
    progress = {
        'completed': completed_orders,
        'failed': failed_orders,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
        json.dump(progress, f, ensure_ascii=False, indent=2)
    logger.info(f"进度已保存: 已完成 {len(completed_orders)} 个订单, 失败 {len(failed_orders)} 个订单")

# 加载进度
def load_progress():
    if os.path.exists(PROGRESS_FILE):
        try:
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                progress = json.load(f)
            logger.info(f"已加载进度: 已完成 {len(progress['completed'])} 个订单, 失败 {len(progress['failed'])} 个订单")
            return progress['completed'], progress['failed']
        except Exception as e:
            logger.error(f"加载进度文件出错: {e}")
    return [], []

# 处理单个订单的异步函数
async def process_order(order_code, order_data):
    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            logger.info(f"开始处理订单: {order_code} (尝试 {retry_count + 1}/{MAX_RETRIES})")

            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=False)
                context = await browser.new_context(viewport={"width": 1280, "height": 950})
                page = await context.new_page()

                # 设置超时
                page.set_default_timeout(30000)

                # 导航到网站
                logger.info(f"正在导航到 {WEBSITE_URL}")
                await page.goto(WEBSITE_URL)

                # 登录
                try:
                    await login(page, USERNAME, PASSWORD)
                    logger.info("登录成功")
                except Exception as e:
                    logger.error(f"登录失败: {e}")
                    await browser.close()
                    retry_count += 1
                    continue

                # 导航到订单创建页面
                first_row = order_data.iloc[0]
                await navigate_to_order_creation(page, first_row)

                # 添加所有商品
                for _, product_row in order_data.iterrows():
                    await add_product(page, product_row)

                # 提交订单并获取订单号
                order_number = await submit_order(page)

                if order_number:
                    logger.info(f"订单创建成功。订单号: {order_number}")
                    # 等待一下确保所有操作完成
                    await asyncio.sleep(2)
                    await browser.close()
                    return order_code, order_number, None  # 成功
                else:
                    logger.warning(f"获取订单号失败")
                    # 等待一下确保所有操作完成
                    await asyncio.sleep(2)
                    await browser.close()
                    retry_count += 1
                    continue

        except Exception as e:
            logger.error(f"处理订单 {order_code} 时出错: {e}")
            # 确保浏览器关闭
            try:
                if 'browser' in locals() and browser is not None:
                    await browser.close()
            except Exception as close_error:
                logger.error(f"关闭浏览器时出错: {close_error}")

            retry_count += 1
            time.sleep(2)  # 出错后等待一段时间再重试

    # 所有重试都失败
    return order_code, None, f"重试 {MAX_RETRIES} 次后仍然失败"

# 主函数
async def main():
    # 读取Excel文件
    logger.info(f"正在读取Excel文件: {EXCEL_FILE_PATH}")
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        logger.info(f"成功读取Excel文件，共{len(df)}行数据")
        logger.info(f"Excel中的列名: {df.columns.tolist()}")
    except Exception as e:
        logger.error(f"读取Excel文件出错: {e}")
        return

    # 检查必要的列是否存在
    required_columns = ['销售单号', '项目', '客户', '收货人', '收货电话', '省/直辖市', '城市',
                        '区/县', '详细地址', '业务员', '商务员', '业务类型',
                        '商品编号', '商品数量', '售价']

    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"缺少必要的列: {missing_columns}")
        return

    # 确保'订单编号'列存在，如果不存在则添加
    if '订单编号' not in df.columns:
        df['订单编号'] = ''
        logger.info("已添加'订单编号'列到数据表中")

    # 按订单编码分组
    order_groups = df.groupby('销售单号')
    logger.info(f"找到{len(order_groups)}个不同的订单需要处理")

    # 加载之前的进度
    completed_orders, failed_orders = load_progress()

    # 过滤掉已完成的订单
    orders_to_process = []
    for order_code, order_data in order_groups:
        if order_code not in completed_orders and order_code not in [f[0] for f in failed_orders]:
            orders_to_process.append((order_code, order_data))

    logger.info(f"需要处理的订单数: {len(orders_to_process)}")

    if not orders_to_process:
        logger.info("没有新订单需要处理")
        return

    # 使用线程池处理订单
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 创建任务列表
        loop = asyncio.get_event_loop()
        tasks = []
        for order_code, order_data in orders_to_process:
            task = loop.create_task(process_order(order_code, order_data))
            tasks.append(task)

        # 处理结果
        processed_count = 0
        for task in asyncio.as_completed(tasks):
            order_code, order_number, error = await task

            if error is None:
                # 订单处理成功
                completed_orders.append(order_code)
                # 更新DataFrame中的订单号
                df.loc[df['销售单号'] == order_code, '订单编号'] = order_number
            else:
                # 订单处理失败
                failed_orders.append((order_code, error))
                logger.error(f"订单 {order_code} 处理失败: {error}")

            processed_count += 1

            # 定期保存进度和更新Excel
            if processed_count % SAVE_INTERVAL == 0:
                save_progress(completed_orders, failed_orders)
                output_file = f"updated_{os.path.basename(EXCEL_FILE_PATH)}"
                df.to_excel(output_file, index=False)
                logger.info(f"已更新Excel文件: {output_file}")

    # 最终保存进度和更新Excel
    save_progress(completed_orders, failed_orders)
    output_file = f"updated_{os.path.basename(EXCEL_FILE_PATH)}"
    df.to_excel(output_file, index=False)
    logger.info(f"所有订单处理完成。更新后的Excel已保存到: {output_file}")

    # 打印失败的订单
    if failed_orders:
        logger.warning("以下订单处理失败:")
        for order_code, error in failed_orders:
            logger.warning(f"  - {order_code}: {error}")

async def login(page, username, password):
    """处理网站登录。根据您网站的登录表单自定义此函数。"""
    # 登录表单交互示例 - 根据您的网站更新选择器
    await page.get_by_placeholder("请输入账号").click()
    await page.get_by_placeholder("请输入账号").fill(username)
    await page.get_by_placeholder("请输入账号").press("Enter")
    await page.get_by_placeholder("请输入账号").press("Tab")
    await page.get_by_placeholder("请输入密码").fill(password)
    await page.get_by_placeholder("请输入密码").press("Enter")
    await page.get_by_role("button", name="添 加").click()

async def navigate_to_order_creation(page, order_data):
    """导航到订单创建页面并填写基本信息。根据您的网站自定义。"""
    # 导航示例 - 根据您的网站更新选择器
    await page.locator(".el-input__inner").first.click()
    await page.locator(".el-input__inner").first.fill(str(order_data["项目"]))
    await page.get_by_text(str(order_data["项目"])).click()
    await page.locator("div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
    await page.get_by_text(str(order_data["客户"])).click()
    await page.get_by_placeholder("请输入内容").click()
    await page.get_by_placeholder("请输入内容").fill(str(order_data['收货人']))
    await page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
    await page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(str(order_data['收货电话']))

    ###选择省市区
    await page.locator(".basic_mess > div:nth-child(2) > div:nth-child(3) > div").click()
    logger.info("已点击地址选择框")
    time.sleep(1)

    # **选择省份**
    await page.get_by_placeholder("省/直辖市").click()
    time.sleep(1)
    await page.get_by_text(str(order_data['省/直辖市']), exact=True).click()
    time.sleep(2)  # 确保城市列表刷新

    # **判断是否为直辖市**
    is_municipality = str(order_data['省/直辖市']) in ["北京市", "上海市", "天津市"]

    # **点击城市输入框**
    await page.get_by_placeholder("城市").click()
    time.sleep(1)

    if is_municipality:
        logger.info(f"直辖市特殊处理: 选择 {str(order_data['省/直辖市'])} 作为城市")
        city_option = page.locator("ul").filter(has_text=re.compile(rf"^{str(order_data['省/直辖市'])}$")).locator("span")
    else:
        city_option = page.get_by_text(str(order_data['城市']), exact=True)

    # **等待城市选项加载并选择**
    await city_option.scroll_into_view_if_needed()
    await city_option.click()
    time.sleep(2)  # **确保区/县数据刷新**

    # **选择区/县**
    await page.get_by_placeholder("区/县").click()
    time.sleep(1)
    await page.get_by_text(str(order_data['区/县']), exact=True).click()
    time.sleep(1)
    await page.get_by_placeholder("请输入200字以内").click()
    await page.get_by_placeholder("请输入200字以内").fill(str(order_data['详细地址']))
    await page.get_by_role("button", name="确 定").click()

async def add_product(page, product_data):
    """向订单添加商品。根据您网站的表单自定义。"""
    try:
        # 添加商品示例 - 根据您的网站更新选择器
        await page.get_by_role("button", name="添加商品").click()
        logger.info(f"正在添加商品: {product_data['商品编号']}")

        # 等待商品表单出现
        await page.wait_for_selector(".el-dialog__body", timeout=5000)

        # 填写商品编号
        await page.get_by_placeholder("请输入商品编号").click()
        # 确保商品编号是字符串并去除小数点
        product_code = str(int(product_data['商品编号'])) if pd.notna(product_data['商品编号']) else ""
        await page.get_by_placeholder("请输入商品编号").fill(product_code)
        await page.get_by_placeholder("请输入商品编号").press("Enter")

        # 等待商品信息加载
        time.sleep(2)

        # 填写数量
        await page.get_by_placeholder("请输入数量").click()
        quantity = str(int(product_data['商品数量'])) if pd.notna(product_data['商品数量']) else "1"
        await page.get_by_placeholder("请输入数量").fill(quantity)

        # 填写售价
        await page.get_by_placeholder("请输入售价").click()
        price = str(float(product_data['售价'])) if pd.notna(product_data['售价']) else "0"
        await page.get_by_placeholder("请输入售价").fill(price)

        # 确认添加商品
        await page.get_by_role("button", name="确 定").click()
        logger.info(f"商品添加成功: {product_data['商品编号']}")

    except Exception as e:
        logger.error(f"添加商品时出错: {e}")
        raise

async def submit_order(page):
    """提交订单并提取订单号。根据您的网站自定义。"""
    try:
        # 点击保存/提交按钮
        logger.info("正在提交订单...")
        await page.get_by_role("button", name="保 存").click()

        # 等待确认对话框
        await page.wait_for_selector(".el-message-box", timeout=10000)

        # 点击确认
        await page.get_by_role("button", name="确 定").click()

        # 等待订单号出现
        await page.wait_for_selector(".el-message--success", timeout=20000)

        # 提取订单号
        success_message = await page.locator(".el-message--success").inner_text()

        # 使用正则表达式提取订单号
        match = re.search(r'单号[：:]\s*([A-Z0-9]+)', success_message)
        if match:
            order_number = match.group(1)
            logger.info(f"成功提取订单号: {order_number}")
            return order_number
        else:
            logger.warning(f"无法从消息中提取订单号: {success_message}")
            return None

    except Exception as e:
        logger.error(f"提交订单时出错: {e}")
        return None

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
