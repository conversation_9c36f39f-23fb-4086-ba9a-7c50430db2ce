
"""
@File        : excel_reader.py
<AUTHOR> yangxue
@Date        : 2025/5/13 9:57
@Description :
@Project     : leading_erpautoui
@Path        : E:\leading_erpautoui\common\excel_reader.py  # 这里自动填充路径
@IDE         :
@Company     : 领先未来-测试部  # 自动填充
"""
import re

import pandas as pd
def ensure_order_column(sheet, column_name="订单编号"):
    """
    自动寻找“订单编号”列索引：若存在则复用，若不存在则写入第一个空白单元格并返回。
    返回值：1-based column index（用于 openpyxl 写入）
    """
    header_row = sheet[1]

    for idx, cell in enumerate(header_row, start=1):
        if cell.value == column_name:
            return idx

    # 如果没有找到，就新增到第一个空列
    for idx, cell in enumerate(header_row, start=1):
        if cell.value is None:
            sheet.cell(row=1, column=idx).value = column_name
            return idx

    # 如果所有列都被占用，则新增在最后一列 + 1
    col_index = len(header_row) + 1
    sheet.cell(row=1, column=col_index).value = column_name
    return col_index

direct_cities = ["北京", "上海", "天津", "重庆"]
provinces = ["北京市", "上海市", "天津市", "重庆市", "内蒙古自治区", "广西壮族自治区", "西藏自治区", "宁夏回族自治区", "新疆维吾尔自治区", "河北省", "山西省", "辽宁省", "吉林省", "黑龙江省", "江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省", "海南省", "四川省", "贵州省", "云南省", "陕西省", "甘肃省", "青海省", "台湾省", "香港特别行政区", "澳门特别行政区"]

def parse_address_to_form_fields(full_address: str):
    full_address = re.sub(r"（.*?）|\(.*?\)", "", full_address).strip()

    # 清理重复片段（如“青海省海西州格尔木市青海省海西州格尔木市...”）
    def remove_repeats(addr):
        for i in range(1, len(addr)):
            prefix = addr[:i]
            if prefix and addr.startswith(prefix * 2):
                return addr[len(prefix):]
        return addr
    full_address = remove_repeats(full_address)

    # 直辖市修复：如“北京西城区...” => “北京市西城区...”
    for dc in direct_cities:
        if full_address.startswith(dc) and not full_address.startswith(dc + "市"):
            full_address = dc + "市" + full_address[len(dc):]

    # 解析规则
    pattern = (
        r"^(?P<province>.+?(省|自治区|特别行政区|市))?"
        r"(?P<city>.+?(自治州|地区|市|盟))?"
        r"(?P<district>.+?(区|县|旗|镇|乡|街道|村|场))?"
        r"(?P<detail>.*)"
    )
    match = re.match(pattern, full_address)
    if not match:
        return "", "", "", full_address

    province = match.group("province") or ""
    city = match.group("city") or ""
    district = match.group("district") or ""
    detail = match.group("detail").strip()

    # 去除 detail 中冗余的省/市/区/县/镇/乡等信息（只去第一次出现）
    for part in [province, city, district]:
        if part:
            detail = detail.replace(part, "", 1)

    detail = detail.strip()

    # 若为直辖市，则 city = province
    for dc in direct_cities:
        if province.startswith(dc):
            if not province.endswith("市"):
                province += "市"
            city = province

    # 所有为空时兜底返回
    if not province and not city and not district:
        return "", "", "", full_address

    return province, city, district, detail

def read_order_data_from_excel(file_path: str) -> list:
    import pandas as pd

    df = pd.read_excel(file_path)

    # ✅ 清洗所有列名：去除 BOM、空格、换行等
    df.columns = df.columns.map(lambda x: str(x).strip().replace("\ufeff", "").replace("\n", ""))

    orders = []

    for index, row in df.iterrows():
        try:
            order = {
                "row_index": index,
                "description": f"{row['客户']} - {row['商品编号']}",
                "input": {
                    "project": row["项目"],
                    "customer": row["客户"],
                    "contact": {
                        "name": row["收货人"],
                        "phone": str(row["收货电话"])
                    },
                    "address": {
                        "province": str(row["省/直辖市"]).strip(),
                        "city": str(row["城市"]).strip(),
                        "district": str(row["区/县"]).strip(),
                        "detail": str(row["详细地址"]).strip()
                    },
                    "business_staff": {
                        "primary": row["业务员"],
                        "secondary": row["商务员"]
                    },
                    "products": [
                        {
                            "name": str(row["商品编号"]),
                            "sku": str(row["商品编号"]),
                            "price": float(row["售价"]),
                            "quantity": int(row["商品数量"])
                        }
                    ]
                },
                "expected": {
                    "success": True,
                    "message": "订单创建成功"
                }
            }
            orders.append(order)
        except KeyError as e:
            raise KeyError(f"⚠ Excel 列名缺失或错误：{e}. 请检查是否有空格或列名错拼。")

    return orders


# def read_order_data_from_excel(file_path: str) -> list:
#     df = pd.read_excel(file_path)
#     orders = []
#
#     for index, row in df.iterrows():
#         full_address = str(row["收货地址"])
#         # addr_parts = full_address.split("-")
#         # province = addr_parts[0] if len(addr_parts) > 0 else ""
#         # city = addr_parts[1] if len(addr_parts) > 1 else ""
#         # district = addr_parts[2] if len(addr_parts) > 2 else ""
#         # detail = "-".join(addr_parts[3:]) if len(addr_parts) > 3 else full_address
#         province, city, district, detail = parse_address_to_form_fields(full_address)
#
#         order = {
#             "row_index": index,  # ✅ 添加原始 Excel 行号
#             "description": f"{row['客户']} - {row['商品编号']}",
#             "input": {
#                 "project": row["项目"],
#                 "customer": row["客户"],
#                 "contact": {
#                     "name": row["收货人"],
#                     "phone": str(row["收货电话"])
#                 },
#                 "address": {
#                     "province": province,
#                     "city": city,
#                     "district": district,
#                     "detail": detail
#                 },
#                 "business_staff": {
#                     "primary": row["业务员"],
#                     "secondary": row["商务员"]
#                 },
#                 "products": [
#                     {
#                         "name": str(row["商品编号"]),
#                         "sku": str(row["商品编号"]),
#                         "price": float(row["售价"]),
#                         "quantity": int(row["商品数量"])
#                     }
#                 ]
#             },
#             "expected": {
#                 "success": True,
#                 "message": "订单创建成功"
#             }
#         }
#
#         orders.append(order)
#
#     return orders

