
import PyPDF2
from PIL import Image
import fitz  # PyMuPDF

def read_pdf(file_path):
    """
    读取PDF文件并提取页面信息
    :param file_path: PDF文件路径
    :return: 返回PDF文件的所有页面内容
    """
    with open(file_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        num_pages = len(reader.pages)
        content = ""
        for i in range(num_pages):
            page = reader.pages[i]
            content += page.extract_text()
        return content

def convert_pdf_to_images(pdf_path):
    """
    将PDF文件的每一页转换为图片并保存到当前目录
    :param pdf_path: PDF文件路径
    """
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    # 遍历每一页
    for page_num in range(len(doc)):
        # 获取当前页
        page = doc.load_page(page_num)
        
        # 将页面转换为图片
        pix = page.get_pixmap()
        
        # 创建Image对象
        image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        
        # 保存图片
        image.save(f"page_{page_num + 1}.png")
        print(f"Page {page_num + 1} has been converted and saved as page_{page_num + 1}.png")

if __name__ == "__main__":
    path = r'G:\paoshuju\签收单-14147-20240914.pdf'
    
    # 读取PDF内容
    pdf_content = read_pdf(path)
    print(pdf_content)
    
    # 转换PDF为图片
    convert_pdf_to_images(path)