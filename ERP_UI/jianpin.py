import tkinter as tk
from tkinter import ttk


class App(tk.Tk):
    def create_widgets(self):
        # 创建表单区域框架
        form_frame = tk.Frame(self, bg="lightgray", padx=30, pady=30)  # 增加内边距
        form_frame.grid(row=0, column=0, sticky="nsew")

        # 用户名输入框
        tk.Label(form_frame, text="Username:", bg="lightgray").grid(row=0, column=0, padx=15, pady=15, sticky="e")
        self.username_entry = tk.Entry(form_frame)
        self.username_entry.insert(0, "liangdan05")
        self.username_entry.grid(row=0, column=1, padx=15, pady=15)

        # 密码输入框
        tk.Label(form_frame, text="Password:", bg="lightgray").grid(row=1, column=0, padx=15, pady=15, sticky="e")
        self.password_entry = tk.Entry(form_frame, show="*")
        self.password_entry.insert(0, "lxwl8888")
        self.password_entry.grid(row=1, column=1, padx=15, pady=15)

        # ID范围输入框
        tk.Label(form_frame, text="Start ID:", bg="lightgray").grid(row=2, column=0, padx=15, pady=15, sticky="e")
        self.id_start_entry = tk.Entry(form_frame)
        self.id_start_entry.grid(row=2, column=1, padx=15, pady=15)

        tk.Label(form_frame, text="End ID:", bg="lightgray").grid(row=3, column=0, padx=15, pady=15, sticky="e")
        self.id_end_entry = tk.Entry(form_frame)
        self.id_end_entry.grid(row=3, column=1, padx=15, pady=15)

        # 查询按钮和执行按钮放在同一行
        self.query_button = tk.Button(form_frame, text="根据id起始值查询", command=self.query_data, bg="#4CAF50", fg="white", font=("Arial", 10))
        self.query_button.grid(row=4, column=0, padx=10, pady=25)

        self.execute_button = tk.Button(form_frame, text="执行", command=self.execute_query, bg="#4CAF50", fg="white", font=("Arial", 10))
        self.execute_button.grid(row=4, column=1, padx=10, pady=25)

        # 结果展示区域
        result_frame = tk.Frame(self, bg="white", padx=20, pady=20)
        result_frame.grid(row=6, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")

        # 创建 Treeview 组件
        self.tree = ttk.Treeview(result_frame, columns=("ser_no", "brand_name", "model", "product_name", "unit", "jd_url", "purchase_price", "website_price", "market_price", "res"), show="headings")
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 设置列标题及宽度
        self.tree.heading("ser_no", text="序号", anchor='center')
        self.tree.column("ser_no", width=50, anchor='center')

        self.tree.heading("brand_name", text="品牌名称", anchor='center')
        self.tree.column("brand_name", width=100, anchor='center')

        self.tree.heading("model", text="型号", anchor='center')
        self.tree.column("model", width=80, anchor='center')

        self.tree.heading("product_name", text="产品名称", anchor='center')
        self.tree.column("product_name", width=150, anchor='center')

        self.tree.heading("unit", text="单位", anchor='center')
        self.tree.column("unit", width=60, anchor='center')

        self.tree.heading("jd_url", text="京东链接", anchor='center')
        self.tree.column("jd_url", width=150, anchor='center')

        self.tree.heading("purchase_price", text="采购价格", anchor='center')
        self.tree.column("purchase_price", width=80, anchor='center')

        self.tree.heading("website_price", text="网站价格", anchor='center')
        self.tree.column("website_price", width=80, anchor='center')

        self.tree.heading("market_price", text="市场价", anchor='center')
        self.tree.column("market_price", width=80, anchor='center')

        self.tree.heading("res", text="备注", anchor='center')
        self.tree.column("res", width=80, anchor='center')

        # 添加垂直滚动条
        scrollbar_y = tk.Scrollbar(result_frame, command=self.tree.yview)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加水平滚动条
        scrollbar_x = tk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 设置表头背景色和文字颜色以提高对比度
        style = ttk.Style()
        style.configure("Treeview.Heading", background="#4CAF50", foreground="white", font=("Arial", 10))
        style.map("Treeview.Heading", background=[('active', '#4CAF50'), ('pressed', '#4CAF50')], foreground=[('active', 'white'), ('pressed', 'white')])
        
        # 设置滚动条样式
        style.configure("Vertical.TScrollbar", troughcolor="lightgray", gripcount=0,
                        background="#4CAF50", darkcolor="gray", lightcolor="gray",
                        troughrelief="flat", borderwidth=1, arrowcolor="white")
        style.configure("Horizontal.TScrollbar", troughcolor="lightgray", gripcount=0,
                        background="#4CAF50", darkcolor="gray", lightcolor="gray",
                        troughrelief="flat", borderwidth=1, arrowcolor="white")

        # 确保 Treeview 和滚动条配置正确
        self.tree.config(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 导出按钮
        self.export_button = tk.Button(self, text="导出执行结果", command=self.export_data, bg="#4CAF50", fg="white", font=("Arial", 10))
        self.export_button.grid(row=7, column=0, columnspan=2, pady=25)

    def query_data(self):
        pass

    def execute_query(self):
        pass

    def export_data(self):
        pass


if __name__ == "__main__":
    app = App()
    app.mainloop()
