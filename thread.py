# encoding: utf-8
# @File  : thread.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/03/23 10:46
import threading
import time
from playwright.sync_api import sync_playwright

def run_erp():
    with sync_playwright() as playwright:
        browser = playwright.chromium.launch(headless=False)
        context = browser.new_context(viewport={"width": 1280, "height": 950})
        page = context.new_page()

        try:
            page.goto("https://lxerptest2.66123123.com/")
            page.goto("https://lxerptest2.66123123.com/#/")
            page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")

            page.get_by_placeholder("请输入账号").click()
            page.get_by_placeholder("请输入账号").fill("liujq")
            page.get_by_placeholder("请输入账号").press("Enter")
            page.get_by_placeholder("请输入账号").fill("liuji<PERSON><PERSON>ang")
            page.get_by_placeholder("请输入账号").press("Tab")
            page.get_by_placeholder("请输入密码").fill("a111111")
            page.get_by_placeholder("请输入密码").press("Enter")

            page.get_by_role("menuitem", name="订单").click()
            page.get_by_text("派单报备").click()
            page.get_by_label("接单中心").get_by_text("接单管理").click()

            # 注册响应监听器
            def handle_response(response):
                if response.ok and 'application/json' in response.headers.get('content-type', ''):
                    try:
                        json_data = response.json()
                        print(response.url + ":" + str(json_data))
                        if isinstance(json_data['data'], list):
                            for item in json_data['data']:
                                if isinstance(item, dict) and item['name'] == '天津市':
                                    print(response.url)
                                    print(item['id'])

                    except Exception as e:
                        print(f"json格式解析失败 {response.url}: {e}")
                else:
                    print(f"响应信息无法转换json {response.url}: {response.status} {response.status_text}")

            page.on("response", handle_response)

            page.get_by_text("发货单管理", exact=True).click()
            time.sleep(5)

        except Exception as e:
            print(f"An error occurred: {e}")

        finally:
            context.close()
            browser.close()

def run_baidu():
    with sync_playwright() as playwright:
        browser = playwright.chromium.launch(headless=False)
        context = browser.new_context(viewport={"width": 1280, "height": 950})
        page = context.new_page()

        try:
            page.goto("https://www.baidu.com/")
            page.locator("#kw").click()
            page.locator("#kw").fill("playwright")
            page.get_by_role("button", name="百度一下").click()
            time.sleep(3)  # 等待搜索结果加载

        except Exception as e:
            print(f"An error occurred: {e}")

        finally:
            context.close()
            browser.close()

def run_all():
    thread_erp = threading.Thread(target=run_erp)
    thread_baidu = threading.Thread(target=run_baidu)

    thread_erp.start()
    thread_baidu.start()

    thread_erp.join()
    thread_baidu.join()

    print("main thread end")

if __name__ == '__main__':
    #run_all()
    num=57
    page=num%10
    print(num//10)
    if page==0:
        page=num//10
        print(page)
    else:
        page = (num//10)+1
        print(page)
