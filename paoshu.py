# encoding: utf-8
# @File  : paoshu.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/04/29 15:14

import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def find_pdf_file_in_subdir(subdir, filename):
    """
    在指定子目录下查找指定的PDF文件是否存在
    :param subdir: 要查找的子目录路径
    :param filename: 要查找的文件名
    :return: 如果文件存在返回True，否则返回False
    """
    for root, dirs, files in os.walk(subdir):
        if filename in files:
            return True
    return False

def find_pdf_file(directory, filename):
    """
    在指定目录下查找指定的PDF文件是否存在，使用多线程优化查找速度
    :param directory: 要查找的目录路径
    :param filename: 要查找的文件名
    :return: 如果文件存在返回True，否则返回False
    """
    with ThreadPoolExecutor() as executor:
        futures = []
        # 首先检查根目录下的文件
        if filename in os.listdir(directory):
            return True
        
        # 然后检查所有子目录
        for root, dirs, files in os.walk(directory):
            for subdir in dirs:
                futures.append(executor.submit(find_pdf_file_in_subdir, os.path.join(root, subdir), filename))
        
        # 等待所有子线程完成
        results = []
        for future in as_completed(futures):
            results.append(future.result())
        
        # 检查是否有任何一个子线程返回True
        return any(results)

if __name__ == "__main__":
    start = time.time()
    directory = r"G:\paoshuju"
    filename = "35242144.pdf"
    result = find_pdf_file(directory, filename)
    end = time.time()
    if result:
        print(f"文件 {filename} 存在于目录 {directory} 中。")
    else:
        print(f"文件 {filename} 不存在于目录 {directory} 中。")
    print(f"查找耗时: {end - start:.6f} 秒")  # 增加时间精度，显示到微秒