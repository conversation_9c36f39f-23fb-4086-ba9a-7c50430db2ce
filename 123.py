# encoding: utf-8
# @File  : 123.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/05/21 14:58
import re
from playwright.sync_api import Playwright, sync_playwright, expect
import time

def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width":1280,"height":950})
    page = context.new_page()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")

    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增接单").click()
    time.sleep(5)

    page.locator("form div").filter(has_text="商品合计： ￥0.00 发货参考毛利率： 0%").nth(1).click(button="right")

    classname = page.locator("//div[@class='cell']/span[contains(text(),'本次售价')]/ancestor::th[1]")
    # name = classname.first.getattribute("class")
    name = classname.first.evaluate('(element) => element.getAttribute("class")')
    print(name)
    print(page.locator(f"//td[@class='{name}']").nth(1))
    page.locator(f"//td[@class='{name}']").nth(1).locator("input").click()
    page.locator(f"//td[@class='{name}']").nth(1).locator("input").fill(123)
    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
