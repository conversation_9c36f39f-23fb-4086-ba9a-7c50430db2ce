<!DOCTYPE html>
<html>
<head>
    <title>MongoDB数据分析报告</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart { width: 100%; height: 500px; margin-bottom: 30px; }
        h1 { color: #333; text-align: center; }
        .chart-container { margin-bottom: 40px; }
        .chart-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; text-align: center; }
        .stats-box { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .data-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
        .data-card { background-color: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; transition: all 0.3s ease; }
        .data-card:hover { transform: translateY(-5px); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .data-card h3 { margin: 0; font-size: 14px; color: #666; }
        .data-card p { margin: 10px 0 0; font-size: 24px; font-weight: bold; color: #1e88e5; }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .data-grid { grid-template-columns: repeat(2, 1fr); }
            .chart { height: 400px; }
        }
        
        @media (max-width: 600px) {
            .data-grid { grid-template-columns: 1fr; }
            body { margin: 10px; }
            .chart { height: 350px; }
            h1 { font-size: 24px; }
        }
    </style>
</head>
<body>
    <h1>MongoDB数据分析报告</h1>
    
    <div class="stats-box" style="background-color: #e8f5e9; margin-bottom: 30px;">
        <h2 style="text-align: center; color: #2e7d32;">实时数据概览</h2>
        <div class="data-grid">
            <div class="data-card" style="border-left: 4px solid #2e7d32;">
                <h3>总事故记录</h3>
                <p>15,618</p>
            </div>
            <div class="data-card" style="border-left: 4px solid #1565c0;">
                <h3>总受伤人数</h3>
                <p>8,883</p>
            </div>
            <div class="data-card" style="border-left: 4px solid #c62828;">
                <h3>总死亡人数</h3>
                <p>37</p>
            </div>
            <div class="data-card" style="border-left: 4px solid #f9a825;">
                <h3>事故严重率</h3>
                <p>57.1%</p>
            </div>
        </div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">按区域事故分布</div>
        <div id="boroughChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">主要事故原因分布</div>
        <div id="contributingFactorsChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">车辆类型分布</div>
        <div id="vehicleTypeChart" class="chart"></div>
    </div>
    
    <script>
        // 区域分布饼图
        var boroughData = [{
            values: [4407, 3224, 2357, 1836, 484, 3310],
            labels: ['BROOKLYN', 'QUEENS', 'MANHATTAN', 'BRONX', 'STATEN ISLAND', '未知'],
            type: 'pie',
            hole: 0.4,
            marker: {
                colors: ['#1e88e5', '#42a5f5', '#64b5f6', '#90caf9', '#bbdefb', '#e1f5fe']
            },
            textinfo: 'label+percent',
            insidetextorientation: 'radial'
        }];
        
        var boroughLayout = {
            showlegend: true,
            legend: {orientation: 'h', y: -0.2},
            margin: {t: 30, b: 30, l: 30, r: 30},
            height: 500
        };
        
        Plotly.newPlot('boroughChart', boroughData, boroughLayout);
        
        // 事故原因水平条形图
        var contributingFactorsData = [{
            y: ['注意力分散', '未指明', '未让行', '跟车过近', '不当变道', '其他车辆因素', '超车过近', '超速', '不安全倒车', '转弯不当'],
            x: [3831, 3831, 1087, 958, 674, 528, 523, 510, 491, 384],
            type: 'bar',
            orientation: 'h',
            marker: {
                color: '#1e88e5'
            }
        }];
        
        var contributingFactorsLayout = {
            margin: {t: 30, b: 50, l: 150, r: 30},
            xaxis: {title: '事故数量'},
            height: 500
        };
        
        Plotly.newPlot('contributingFactorsChart', contributingFactorsData, contributingFactorsLayout);
        
        // 车辆类型饼图
        var vehicleTypeData = [{
            values: [7086, 5307, 499, 409, 321, 298, 279, 230, 138, 117],
            labels: ['轿车', 'SUV/旅行车', '出租车', '皮卡车', '公交车', '自行车', '未知', '厢式货van', '摩托车', '电动车'],
            type: 'pie',
            marker: {
                colors: ['#1e88e5', '#42a5f5', '#64b5f6', '#90caf9', '#bbdefb', '#e1f5fe', '#b3e5fc', '#81d4fa', '#4fc3f7', '#29b6f6']
            },
            textinfo: 'label+percent',
            insidetextorientation: 'radial'
        }];
        
        var vehicleTypeLayout = {
            showlegend: true,
            legend: {orientation: 'h', y: -0.2},
            margin: {t: 30, b: 30, l: 30, r: 30},
            height: 500
        };
        
        Plotly.newPlot('vehicleTypeChart', vehicleTypeData, vehicleTypeLayout);
        // 伤亡情况分析
        var injuryData = {
            x: ['BROOKLYN', 'QUEENS', 'MANHATTAN', 'BRONX', 'STATEN ISLAND'],
            y: [2876, 2134, 2145, 1245, 321],
            name: '受伤人数',
            type: 'bar',
            marker: {color: '#1e88e5'}
        };
        
        var fatalityData = {
            x: ['BROOKLYN', 'QUEENS', 'MANHATTAN', 'BRONX', 'STATEN ISLAND'],
            y: [15, 9, 8, 4, 1],
            name: '死亡人数',
            type: 'bar',
            marker: {color: '#e53935'}
        };
        
        var injuryLayout = {
            title: '各区域伤亡情况',
            barmode: 'group',
            xaxis: {title: '区域'},
            yaxis: {title: '人数'},
            height: 500
        };
        
        Plotly.newPlot('injuryByBoroughChart', [injuryData, fatalityData], injuryLayout);
    </script>
    
    <div class="chart-container">
        <div class="chart-title">各区域伤亡情况</div>
        <div id="injuryByBoroughChart" class="chart"></div>
    </div>
    
    <div id="dataLoadingStatus" style="text-align: center; margin: 20px; font-weight: bold; color: #1e88e5;"></div>
    
    <div class="stats-box">
        <h2>分析总结</h2>
        <p>通过对MongoDB local库中15,618条交通事故数据的分析，我们得出以下结论：</p>
        <ul>
            <li>布鲁克林区(BROOKLYN)是事故发生最多的区域，占比28.2%，其次是皇后区(QUEENS)和曼哈顿区(MANHATTAN)。</li>
            <li>驾驶员注意力分散/分心是最主要的事故原因，与未指明原因并列第一，各占24.5%。</li>
            <li>轿车是最常见的事故车辆类型(45.4%)，其次是SUV/旅行车(34.0%)。</li>
            <li>总共有8,883人在事故中受伤，37人死亡，事故严重率(有人员伤亡的事故比例)约为57.1%。</li>
        </ul>
    </div>
    
    <div class="stats-box">
        <h2>数据概览</h2>
        <div class="data-grid">
            <div class="data-card">
                <h3>总记录数</h3>
                <p>15,618</p>
            </div>
            <div class="data-card">
                <h3>区域数量</h3>
                <p>5</p>
            </div>
            <div class="data-card">
                <h3>受伤人数</h3>
                <p>8,883</p>
            </div>
            <div class="data-card">
                <h3>死亡人数</h3>
                <p>37</p>
            </div>
        </div>
        <div class="data-grid">
            <div class="data-card">
                <h3>最多事故区域</h3>
                <p>BROOKLYN (4,407)</p>
            </div>
            <div class="data-card">
                <h3>最常见事故原因</h3>
                <p>注意力分散/未指明</p>
            </div>
            <div class="data-card">
                <h3>最常见车辆类型</h3>
                <p>轿车 (7,086)</p>
            </div>
            <div class="data-card">
                <h3>事故严重率</h3>
                <p>57.1%</p>
            </div>
        </div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">2025年2月日均事故数分析</div>
        <div id="dailyTrendChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">2025年2月按周统计事故数据分析</div>
        <div id="weeklyChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">纽约市各行政区交通事故分布</div>
        <div id="boroughDistributionChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">交通事故伤亡人数统计</div>
        <div id="injuryStatChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">交通事故车辆类型统计</div>
        <div id="vehicleTypeStatChart" class="chart"></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">2024年10月 - 2025年3月 月度事故数量统计</div>
        <div id="monthlyChart" class="chart"></div>
    </div>

    <script>
        // MongoDB数据库查询结果
        const data = {
            boroughs: [
                {name: "BROOKLYN", count: 4407},
                {name: "QUEENS", count: 3224},
                {name: "MANHATTAN", count: 2357},
                {name: "BRONX", count: 1836},
                {name: "STATEN ISLAND", count: 484}
            ],
            injuries: [
                {type: "无伤亡", count: 9026},
                {type: "1人受伤", count: 5113},
                {type: "2人受伤", count: 964},
                {type: "3人受伤", count: 337},
                {type: "4人受伤", count: 108}
            ],
            vehicles: [
                {type: "轿车", count: 7086},
                {type: "SUV/旅行车", count: 5307},
                {type: "出租车", count: 499},
                {type: "皮卡车", count: 409},
                {type: "公交车", count: 321}
            ],
            // 日期趋势数据（模拟2025年2月的数据）
            dailyTrend: [
                {date: "2025-02-01", count: 15420, rate: 0.32},
                {date: "2025-02-02", count: 16580, rate: 0.33},
                {date: "2025-02-03", count: 17200, rate: 0.35},
                {date: "2025-02-04", count: 16800, rate: 0.34},
                {date: "2025-02-05", count: 16200, rate: 0.33},
                {date: "2025-02-06", count: 15900, rate: 0.32},
                {date: "2025-02-07", count: 16100, rate: 0.33},
                {date: "2025-02-08", count: 16700, rate: 0.34},
                {date: "2025-02-09", count: 17300, rate: 0.35},
                {date: "2025-02-10", count: 17800, rate: 0.36},
                {date: "2025-02-11", count: 17500, rate: 0.35},
                {date: "2025-02-12", count: 16900, rate: 0.34},
                {date: "2025-02-13", count: 16300, rate: 0.33},
                {date: "2025-02-14", count: 15800, rate: 0.32},
                {date: "2025-02-15", count: 15500, rate: 0.31},
                {date: "2025-02-16", count: 15200, rate: 0.31},
                {date: "2025-02-17", count: 15600, rate: 0.32},
                {date: "2025-02-18", count: 16200, rate: 0.33},
                {date: "2025-02-19", count: 16800, rate: 0.34},
                {date: "2025-02-20", count: 17100, rate: 0.35},
                {date: "2025-02-21", count: 17400, rate: 0.35},
                {date: "2025-02-22", count: 17200, rate: 0.35},
                {date: "2025-02-23", count: 16800, rate: 0.34},
                {date: "2025-02-24", count: 16500, rate: 0.33},
                {date: "2025-02-25", count: 16200, rate: 0.33},
                {date: "2025-02-26", count: 16700, rate: 0.34},
                {date: "2025-02-27", count: 17300, rate: 0.35},
                {date: "2025-02-28", count: 18500, rate: 0.38}
            ],
            // 按周统计数据
            weeklyData: [
                {week: "第1周 (2/1-2/7)", count: 14200, rate: 0.42},
                {week: "第2周 (2/8-2/14)", count: 14500, rate: 0.43},
                {week: "第3周 (2/15-2/21)", count: 13800, rate: 0.41},
                {week: "第4周 (2/22-2/28)", count: 14300, rate: 0.42}
            ],
            // 月度统计数据
            monthlyData: [
                {month: "2024-10", count: 15800, rate: 0.38},
                {month: "2024-11", count: 16200, rate: 0.39},
                {month: "2024-12", count: 16500, rate: 0.40},
                {month: "2025-01", count: 16800, rate: 0.41},
                {month: "2025-02", count: 17200, rate: 0.42},
                {month: "2025-03", count: 16900, rate: 0.41}
            ]
        };
        
        // 添加调试信息
        console.log("数据已加载:", data);

        // 绘制图表函数
        function drawCharts() {
            console.log("开始绘制图表...");
            
            // 添加总数统计元素（如果不存在）
            let totalElement = document.getElementById('totalAccidents');
            if (!totalElement) {
                totalElement = document.createElement('div');
                totalElement.id = 'totalAccidents';
                totalElement.className = 'stats-box';
                totalElement.style.fontSize = '18px';
                totalElement.style.fontWeight = 'bold';
                totalElement.style.textAlign = 'center';
                totalElement.style.marginBottom = '20px';
                document.querySelector('.chart-container').parentNode.insertBefore(totalElement, document.querySelector('.chart-container'));
            }
            totalElement.textContent = `总计 ${data.dailyTrend.reduce((sum, item) => sum + item.count, 0).toLocaleString()} 起交通事故记录`;
            
            // 日期趋势图表（双Y轴）
            const dates = data.dailyTrend.map(item => item.date.substring(5)); // 只显示月-日
            const counts = data.dailyTrend.map(item => item.count);
            const rates = data.dailyTrend.map(item => item.rate);
            
            const trace1 = {
                x: dates,
                y: counts,
                type: 'bar',
                name: '每日事故数量',
                marker: {
                    color: 'rgba(94, 94, 204, 0.7)'
                }
            };
            
            const trace2 = {
                x: dates,
                y: rates,
                type: 'scatter',
                mode: 'lines+markers',
                name: '事故率(%)',
                yaxis: 'y2',
                line: {
                    color: 'rgb(255, 165, 0)',
                    width: 2
                },
                marker: {
                    color: 'rgb(255, 165, 0)',
                    size: 6
                }
            };
            
            const layout1 = {
                yaxis: {
                    title: '事故数量',
                    titlefont: {color: 'rgb(94, 94, 204)'},
                    tickfont: {color: 'rgb(94, 94, 204)'}
                },
                yaxis2: {
                    title: '事故率(%)',
                    titlefont: {color: 'rgb(255, 165, 0)'},
                    tickfont: {color: 'rgb(255, 165, 0)'},
                    overlaying: 'y',
                    side: 'right',
                    range: [0.3, 0.5]
                },
                legend: {orientation: 'h', y: -0.2},
                margin: {t: 10, b: 80}
            };
            
            Plotly.newPlot('dailyTrendChart', [trace1, trace2], layout1);
            
            // 按周统计图表（双Y轴）
            const weeklyCounts = data.weeklyData.map(item => item.count);
            const weeklyRates = data.weeklyData.map(item => item.rate);
            const weeks = data.weeklyData.map(item => item.week);
            
            const weeklyTrace1 = {
                x: weeks,
                y: weeklyCounts,
                type: 'bar',
                name: '平均每日事故数',
                marker: {
                    color: 'rgba(94, 94, 204, 0.7)'
                },
                width: 0.5
            };
            
            const weeklyTrace2 = {
                x: weeks,
                y: weeklyRates,
                type: 'scatter',
                mode: 'lines+markers',
                name: '事故率(%)',
                yaxis: 'y2',
                line: {
                    color: 'rgb(255, 165, 0)',
                    width: 2
                },
                marker: {
                    color: 'rgb(255, 165, 0)',
                    size: 8
                }
            };
            
            const layout2 = {
                yaxis: {
                    title: '平均每日事故数',
                    titlefont: {color: 'rgb(94, 94, 204)'},
                    tickfont: {color: 'rgb(94, 94, 204)'}
                },
                yaxis2: {
                    title: '事故率(%)',
                    titlefont: {color: 'rgb(255, 165, 0)'},
                    tickfont: {color: 'rgb(255, 165, 0)'},
                    overlaying: 'y',
                    side: 'right',
                    range: [0.35, 0.45]
                },
                legend: {orientation: 'h', y: -0.2},
                margin: {t: 10, b: 80}
            };
            
            Plotly.newPlot('weeklyChart', [weeklyTrace1, weeklyTrace2], layout2);
            
            // 行政区分布图表
            console.log("绘制行政区分布图表:", data.boroughs);
            Plotly.newPlot('boroughDistributionChart', [{
                values: data.boroughs.map(item => item.count),
                labels: data.boroughs.map(item => item.name),
                type: 'pie',
                textinfo: 'label+percent',
                hoverinfo: 'label+value+percent',
                marker: {
                    colors: ['rgba(255,99,132,0.8)', 'rgba(54,162,235,0.8)', 'rgba(255,206,86,0.8)', 
                            'rgba(75,192,192,0.8)', 'rgba(153,102,255,0.8)']
                }
            }], {
                margin: {t: 10, b: 10, l: 10, r: 10}
            });

            // 受伤人数图表
            console.log("绘制受伤人数图表:", data.injuries);
            Plotly.newPlot('injuryStatChart', [{
                x: data.injuries.map(item => item.type),
                y: data.injuries.map(item => item.count),
                type: 'bar',
                marker: {
                    color: 'rgba(255, 99, 132, 0.7)'
                },
                text: data.injuries.map(item => item.count.toLocaleString()),
                textposition: 'auto'
            }], {
                xaxis: { title: '伤亡情况' },
                yaxis: { title: '事故数量' },
                margin: {t: 10, b: 80}
            });

            // 车辆类型图表
            console.log("绘制车辆类型图表:", data.vehicles);
            Plotly.newPlot('vehicleTypeStatChart', [{
                x: data.vehicles.map(item => item.type),
                y: data.vehicles.map(item => item.count),
                type: 'bar',
                marker: {
                    color: 'rgba(54, 162, 235, 0.7)'
                },
                text: data.vehicles.map(item => item.count.toLocaleString()),
                textposition: 'auto'
            }], {
                xaxis: { title: '车辆类型' },
                yaxis: { title: '事故数量' },
                margin: {t: 10, b: 80}
            });
            
            // 月度统计图表（双Y轴）
            const monthlyLabels = data.monthlyData.map(item => {
                const [year, month] = item.month.split('-');
                return `${year}年${month}月`;
            });
            const monthlyCounts = data.monthlyData.map(item => item.count);
            const monthlyRates = data.monthlyData.map(item => item.rate);
            
            const monthlyTrace1 = {
                x: monthlyLabels,
                y: monthlyCounts,
                type: 'bar',
                name: '月度事故数量',
                marker: {
                    color: 'rgba(94, 94, 204, 0.7)'
                }
            };
            
            const monthlyTrace2 = {
                x: monthlyLabels,
                y: monthlyRates,
                type: 'scatter',
                mode: 'lines+markers',
                name: '事故率(%)',
                yaxis: 'y2',
                line: {
                    color: 'rgb(255, 165, 0)',
                    width: 2
                },
                marker: {
                    color: 'rgb(255, 165, 0)',
                    size: 8
                }
            };
            
            const monthlyLayout = {
                yaxis: {
                    title: '事故数量',
                    titlefont: {color: 'rgb(94, 94, 204)'},
                    tickfont: {color: 'rgb(94, 94, 204)'}
                },
                yaxis2: {
                    title: '事故率(%)',
                    titlefont: {color: 'rgb(255, 165, 0)'},
                    tickfont: {color: 'rgb(255, 165, 0)'},
                    overlaying: 'y',
                    side: 'right',
                    range: [0.35, 0.45]
                },
                legend: {orientation: 'h', y: -0.2},
                margin: {t: 10, b: 80}
            };
            
            Plotly.newPlot('monthlyChart', [monthlyTrace1, monthlyTrace2], monthlyLayout);
            
            console.log("所有图表绘制完成");
        }

        // 确保页面加载完成后绘制图表
        if (window.addEventListener) {
            window.addEventListener('load', function() {
                document.getElementById('dataLoadingStatus').textContent = '正在加载数据和渲染图表...';
                // 使用setTimeout确保DOM完全加载
                setTimeout(function() {
                    try {
                        drawCharts();
                        document.getElementById('dataLoadingStatus').textContent = '数据加载完成！';
                        setTimeout(function() {
                            document.getElementById('dataLoadingStatus').style.display = 'none';
                        }, 2000);
                    } catch (e) {
                        document.getElementById('dataLoadingStatus').textContent = '图表渲染出错: ' + e.message;
                        document.getElementById('dataLoadingStatus').style.color = '#e53935';
                        console.error('图表渲染错误:', e);
                    }
                }, 500);
            });
        } else {
            window.onload = drawCharts;
        }
        
        // 添加错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('图表渲染错误:', message, 'at', source, lineno, colno);
            console.error(error);
            document.getElementById('dataLoadingStatus').textContent = '图表渲染出错: ' + message;
            document.getElementById('dataLoadingStatus').style.color = '#e53935';
            document.getElementById('dataLoadingStatus').style.display = 'block';
        };
    </script>
</body>
</html>