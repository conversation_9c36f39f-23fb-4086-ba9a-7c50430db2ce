# encoding: utf-8
# @File  : demo1.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/03/21 21:29
from playwright.sync_api import sync_playwright
import pandas as pd


def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width": 1280, "height": 950})
    page = context.new_page()

    # 假设商品列表页面的 URL 是 https://example-ecommerce.com/products
    page.goto("https://example-ecommerce.com/products")

    # 获取所有商品链接
    product_links = page.eval_on_selector_all('a.product-link', 'elements => elements.map(element => element.href)')

    data = []

    for link in product_links:
        page.goto(link)

        # 提取商品信息
        try:
            image_url = page.get_by_role("img", name="product-image").get_attribute("src")
            skuno = page.get_by_text("SKU:").text_content().split(":")[1].strip()
            price = page.get_by_text("Price:").text_content().split(":")[1].strip()
            detail_url = page.url

            data.append({
                "Image URL": image_url,
                "SKU No": skuno,
                "Price": price,
                "Detail URL": detail_url
            })
        except Exception as e:
            print(f"Error extracting data from {link}: {e}")

    # 将数据保存到 CSV 文件
    df = pd.DataFrame(data)
    df.to_csv("products.csv", index=False)

    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
