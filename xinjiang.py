# encoding: utf-8
# @File  : xinjiang.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/05/20 10:37
import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")
    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增接单").click()
    page.get_by_role("button", name="添 加").click()
    page.locator(".el-input__inner").first.click()
    page.get_by_text("五原医药").click()
    page.locator("div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
    page.get_by_text("内蒙古泰达新能源有限公司").nth(1).click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_placeholder("请输入内容").fill("1")
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill("1")
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
    page.locator(".chunk > div > span").click()
    page.get_by_placeholder("省/直辖市").click()
    page.get_by_text("新疆维吾尔自治区").click()
    page.get_by_placeholder("城市").click()
    page.get_by_text("新疆维吾尔自治区自治区直辖县级行政区划").click()
    page.get_by_placeholder("区/县").click()
    page.get_by_text("可克达拉市").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
