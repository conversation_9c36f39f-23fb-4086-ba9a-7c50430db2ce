import json
import re
import time

import pandas as pd
import requests
from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    # context = browser.new_context(viewport={'width': 1920, 'height': 1080})
    context = browser.new_context(viewport={'width': 1920, 'height': 950})

    context.tracing.start(screenshots=True, snapshots=True, sources=True)
    page = context.new_page()
    page.goto("https://lxerppre.66123123.com/")
    page.goto("https://lxerppre.66123123.com/#/")
    page.goto("https://lxerppre.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.goto("https://lxerppre.66123123.com/")
    page.goto("https://lxerppre.66123123.com/#/")
    page.goto("https://lxerppre.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("abc123")
    page.get_by_role("button", name="登 录").click()
    page.get_by_role("button", name="添 加").click()
    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增接单").click()
    #page.get_by_role("menuitem", name="商品").click()

    file_path='./新建订单.xlsx'
    df=pd.read_excel(file_path,sheet_name='Sheet1',header=None,skiprows=1)
    for index, row in df.iterrows():
        go(page, row)

    context.tracing.stop(path="trace.zip")
    # 如果不需要在测试结束后保持浏览器打开，可以取消注释以下两行
    # context.close()
    # browser.close()

usreinfo=''
def go(page,row):
    xiangmu=row[1]
    kehu=row[2]
    shouhuoren=row[3]
    phone=str(row[4])
    adds=row[5].split("-")
    sheng=adds[0]
    shi=adds[1]
    qu=adds[2]
    xiangxi=adds[3]
    yewuyuan=row[6]
    shangwuyuan=row[7]
    skuno=str(row[9])
    shoujia=str(row[10])
    shuliang=str(row[11])

    # page.get_by_role("menuitem", name="订单").click()
    # page.get_by_text("新增接单").click()
    page.locator(".el-input__inner").first.click()
    page.get_by_text(xiangmu).click()
    page.locator(
        "div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
    page.get_by_text(kehu, exact=True).click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_placeholder("请输入内容").fill(shouhuoren)
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(phone)
    page.locator(".basic_mess > div:nth-child(2) > div:nth-child(3) > div").click()
    page.get_by_placeholder("省/直辖市").click()
    page.get_by_text(sheng).click()
    page.get_by_placeholder("城市").click()
    time.sleep(1)
    print(shi)

    page.get_by_text(shi).click()
    page.get_by_placeholder("区/县").click()
    page.get_by_text(qu).click()
    page.get_by_placeholder("请输入200字以内").click()
    page.get_by_placeholder("请输入200字以内").fill(xiangxi)
    page.get_by_role("button", name="确 定").click()
    page.locator(
        "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
    with page.expect_response("**/api/user-service/user/organization/user-list") as response_info:
        page.locator(
            "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
            "刘建强")
    response = response_info.value
    json_data=response.json()
    usreinfo=json_data['data'][0]['userName']+'-'+json_data['data'][0]['realName']
    print(usreinfo)
    page.get_by_text(usreinfo).click()
    page.locator(
        "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
    page.locator(
        "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
        "刘建强")
    page.get_by_text("liujianqiang-刘建强").nth(1).click()

    page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).click()
    page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).fill(skuno)
    skuname={}

    def handle_response(response):
        if response.ok and 'application/json' in response.headers.get('content-type', ''):
            try:
                json_data = response.json()
                print(response.url + ":" + str(json_data))
                print(json_data['data'][0])
                # orderDeliveryNoList=[]
                data = json_data['data'][0]
                print(data)
                skuname['skuname'] = data['skuName']
                print(data['skuName'])
                #if isinstance(json_data['data'], dict) and 'associate-list' in response.url:

                    # for item in json_data['data'][0]:
                    #     print(item)
                    #     if isinstance(item, dict):
                    #        skuname['skuname'] = item['skuName']
                    #        print( item['skuName'])

                    # orderDeliveryNoList.append(res)
                    #print('orderDeliveryNoList：' + ','.join(orderDeliveryNoList))
            except Exception as e:
                print(f"json格式解析失败 {response.url}: {e}")
        else:
            print(f"响应信息无法转换json {response.url}: {response.status} {response.status_text}")

    page.on("response", handle_response)
    page.get_by_text("10066659").nth(3).click()
    page.get_by_role("row", name=re.compile(skuno)).get_by_placeholder("字数长度1~").nth(1).dblclick()
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(1).fill("76016.0000")
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(1).dblclick()
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(1).dblclick()
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(1).fill(shoujia)
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(2).click()
    page.get_by_role("row", name=skuname['skuname']).get_by_placeholder("字数长度1~").nth(2).fill(shuliang)
    page.locator(".saleDown > .clearfix").click()
    page.get_by_role("button", name="保存订单").click()
    page.get_by_role("button", name="确 定").click()
    # input('111111111111111111')

    # ---------------------
    # context.close()
    # browser.close()
def send_dingtalk_message(webhook_url, message):
    """
    发送消息到钉钉群的函数

    :param webhook_url: 钉钉机器人的Webhook URL
    :param message: 要发送的消息内容
    """
    # 构建消息体
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        },
        "at": {
            "isAtAll": False  # 默认不@所有人
        }
    }

    # 发送POST请求
    headers = {'Content-Type': 'application/json'}
    response = requests.post(webhook_url, headers=headers, data=json.dumps(data))

    # 打印响应结果
    if response.ok:
        print("消息发送成功")
    else:
        print("消息发送失败")




with sync_playwright() as playwright:
    run(playwright)

# if __name__ == '__main__':
#     file_path = './新建订单.xlsx'
#     df = pd.read_excel(file_path, sheet_name='Sheet1', header=None, nrows=10, skiprows=1)
#     for index, row in df.iterrows():
#         print(row[1], row[2], row[3], row[4], row[5],row[6],row[7],row[9],row[10],row[11])
#         adds=row[5].split('-')
#         print(adds[0],adds[1],adds[2],adds[3])