
import re

def parse_address(address):
    # 调整正则表达式以正确匹配区的部分
    pattern = r'^(?P<province>[^省]+省)?(?P<city>[^市]+市)?(?P<district>(?:[^区]+区|[^市]+市))(?P<sub_district>[^区]*)?(?P<detail>.*)$'
    match = re.match(pattern, address)
    if match:
        result = match.groupdict()
        
        # 修正区值处理逻辑，确保只包含正确的区名
        result['district'] = result['district'].replace('区', '') if '区' in result['district'] else result['district']
        result['detail'] = result['sub_district'] + result['detail'] if result['sub_district'] else result['detail']
        
        return result
    return None


address = "江苏省苏州市常熟市经开区兴港路28号"
parsed_address = parse_address(address)
print(f"原始地址：{address}")
print(f"省：{parsed_address['province']}，市：{parsed_address['city']}，区：{parsed_address['district']}，地址：{parsed_address['detail']}")
