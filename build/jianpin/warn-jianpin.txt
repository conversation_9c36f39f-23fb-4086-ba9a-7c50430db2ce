
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.splittype - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.ContentTooShortError - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.pathname2url - imported by url<PERSON><PERSON> (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.url2pathname - imported by urllib (conditional), pip._vendor.distlib.compat (conditional), docutils.parsers.rst.directives.images (conditional), docutils.writers._html_base (conditional), docutils.writers.latex2e (conditional)
missing module named urllib.urlretrieve - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.unquote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), sqlalchemy.util.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional), docutils.utils.math.math2html (conditional)
missing module named urllib.quote - imported by urllib (conditional), sqlalchemy.util.compat (conditional), send2trash.plat_other (optional), pip._vendor.distlib.compat (conditional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), _pytest._py.path (delayed), distutils.archive_util (optional), twisted.python.util (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), psutil (optional), netrc (delayed, conditional), getpass (delayed), _pytest._py.path (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), docutils.frontend (delayed, conditional, optional), conda.core.envs_manager (delayed, conditional), twisted.python.util (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), ptyprocess.ptyprocess (top-level), IPython.utils.timing (optional), distributed.utils (optional), distributed.system (delayed, conditional, optional), jupyter_server.serverapp (optional), prometheus_client.process_collector (optional), fsspec.asyn (optional), twisted.internet.process (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level), jupyter_server.utils (top-level), pip._vendor.distlib.resources (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), pty (delayed, optional), ptyprocess.ptyprocess (top-level), xmlrpc.server (optional), sphinx.util.console (delayed, optional), zmq.eventloop.minitornado.platform.posix (top-level), paramiko.agent (delayed), tqdm.utils (delayed, optional), locket (optional), conda.gateways.repodata.lock (optional), twisted.python.compat (delayed, optional), twisted.internet.fdesc (optional), twisted.internet.process (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed), pip._vendor.distlib.scripts (delayed, conditional)
missing module named _winreg - imported by platform (delayed, optional), numexpr.cpuinfo (delayed, optional), selenium.webdriver.firefox.firefox_binary (delayed, optional), conda._vendor.appdirs (delayed), conda._vendor.cpuinfo.cpuinfo (delayed, optional), cpuinfo.cpuinfo (delayed, optional), pygments.formatters.img (optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), psutil._compat (delayed, optional), getpass (optional), ptyprocess.ptyprocess (top-level), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level), sphinx.util.console (delayed, optional), click._termui_impl (conditional), tqdm.utils (delayed, optional), twisted.internet.process (optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), _pytest.capture (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional), dill.source (delayed, conditional, optional), sphinx.cmd.quickstart (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (delayed), conda._vendor.cpuinfo.cpuinfo (delayed), cpuinfo.cpuinfo (delayed)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level), partd.zmq (top-level), conda._vendor.cpuinfo.cpuinfo (delayed), cpuinfo.cpuinfo (delayed)
missing module named multiprocessing.freeze_support - imported by multiprocessing (delayed, conditional), black (delayed, conditional), numba.runtests (conditional)
missing module named multiprocessing.Manager - imported by multiprocessing (top-level), black.concurrency (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pip._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command.egg_info (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level), setuptools.depends (top-level)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named jnius - imported by platformdirs.android (delayed, optional), pip._vendor.platformdirs.android (delayed, optional), pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools.config.setupcfg (top-level), setuptools._reqs (top-level)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools.config.setupcfg (top-level)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (optional), tables.tests.common (delayed, optional)
missing module named pyimod02_importers - imported by D:\anaconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\anaconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named simplejson - imported by requests.compat (conditional, optional), jsonpath (conditional, optional)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional), sqlalchemy.util.compat (optional), pip._vendor.requests.cookies (optional), pip._vendor.distlib.util (optional)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional), partd.compatibility (conditional), sqlalchemy.util.compat (conditional), pip._vendor.urllib3.packages.six (conditional), pip._vendor.six (conditional), pip._vendor.distlib.compat (conditional), botocore.vendored.six (conditional), docutils.writers.docutils_xml (conditional), docutils.writers.odf_odt (conditional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), 'urllib3.packages.six.moves.urllib' (top-level), urllib3.util.queue (top-level)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named Queue - imported by urllib3.util.queue (conditional), numba.testing.main (optional), partd.compatibility (conditional), pip._vendor.urllib3.util.queue (conditional), pip._vendor.distlib.compat (conditional)
missing module named "'urllib3.packages.six.moves.urllib'.parse" - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional), pip._vendor.urllib3.contrib.pyopenssl (optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named urllib3_secure_extra - imported by urllib3 (optional), pip._vendor.urllib3 (optional)
missing module named PyObjCTools - imported by jpype._gui (delayed, conditional)
missing module named 'jedi.access' - imported by jpype._core (optional)
missing module named jinja2.contextfunction - imported by jinja2 (optional), sphinx.jinja2glue (optional)
missing module named jinja2.environmentfilter - imported by jinja2 (optional), sphinx.util.rst (optional)
missing module named 'sphinx.domain' - imported by sphinx.transforms (conditional), sphinx.util.nodes (conditional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named ConfigParser - imported by docutils.frontend (conditional), yapf.yapflib.py3compat (conditional), sqlalchemy.util.compat (conditional), pip._vendor.distlib.compat (conditional), docutils.writers.odf_odt (conditional)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.lowering (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), sphinx.highlighting (top-level)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed), sphinx.highlighting (top-level), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.lowering (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.lexers.TextLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.RstLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.PythonConsoleLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level), sphinx.transforms.post_transforms.code (top-level)
missing module named pygments.lexers.Python3Lexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.CLexer - imported by pygments.lexers (top-level), sphinx.highlighting (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level), sphinx.highlighting (top-level), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._imagingcms - imported by PIL (optional), PIL.ImageCms (optional)
missing module named numpy.arctanh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arccosh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arctan - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.tan - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.fmod - imported by numpy (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.hypot - imported by numpy (top-level), scipy.stats._morestats (top-level)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level), scipy.fftpack._pseudo_diffs (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level), scipy.fftpack._pseudo_diffs (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level), scipy.fftpack._pseudo_diffs (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.NINF - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.double - imported by numpy (top-level), scipy.optimize._nnls (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.random.randn - imported by numpy.random (top-level), scipy (top-level)
missing module named numpy.random.rand - imported by numpy.random (top-level), scipy (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional), cloudpickle.compat (conditional, optional), numba.cloudpickle.compat (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg._decomp (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level), scipy.interpolate._pade (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.stats._stats_py (top-level), dill._objects (optional), scipy.linalg._decomp (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp_schur (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), numexpr.tests.test_numexpr (top-level), numba.cuda.vectorizers (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level), scipy.fftpack._pseudo_diffs (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy.optimize._lbfgsb_py (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numexpr.tests.test_numexpr (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), dill._objects (optional), numexpr.tests.test_numexpr (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), dill._dill (delayed), scipy.optimize._minpack_py (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), _pytest.python_api (conditional), IPython.core.magics.namespace (delayed, conditional, optional), dill._dill (delayed), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), dill._dill (delayed), dill._objects (optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional), pip._vendor.distlib.compat (optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional), yapf.yapflib.py3compat (conditional), paramiko.py3compat (conditional), sqlalchemy.util.compat (conditional), cpuinfo.cpuinfo (delayed, conditional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional), pickleshare (optional), partd.compatibility (conditional), sqlalchemy.util.compat (conditional, optional), pip._vendor.cachecontrol.compat (optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional), pip._vendor.distlib.compat (conditional)
missing module named typed_ast - imported by sphinx.pycode.ast (conditional, optional), black.parsing (optional)
missing module named sphinx_rtd_theme - imported by sphinx.theming (delayed, optional)
missing module named Stemmer - imported by sphinx.util.stemmer (optional), snowballstemmer (optional)
missing module named urllib2 - imported by docutils.parsers.rst.directives.misc (delayed, conditional), pip._vendor.distlib.compat (conditional), docutils.writers.odf_odt (conditional), docutils.parsers.rst.directives.tables (delayed, conditional), lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named Image - imported by docutils.parsers.rst.directives.images (optional)
missing module named roman - imported by docutils.writers.latex2e (optional), docutils.writers.manpage (optional), sphinx.writers.latex (optional)
missing module named 'requests.packages.urllib3' - imported by conda.gateways.connection (optional), sphinx.util.requests (optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named subunit - imported by twisted.trial.reporter (optional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named exceptiongroup - imported by _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), trio._core._run (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional), _pytest.unittest (conditional)
missing module named tomli - imported by _pytest.config.findpaths (delayed, conditional), black.files (conditional, optional)
missing module named argcomplete - imported by _pytest._argcomplete (conditional, optional)
missing module named __builtin__ - imported by ptyprocess.ptyprocess (optional), yapf.yapflib.py3compat (conditional), paramiko.py3compat (conditional), lmdb.cffi (optional), ipython_genutils.py3compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named jieba - imported by sphinx.search.zh (optional)
missing module named janome - imported by sphinx.search.ja (optional)
missing module named MeCab - imported by sphinx.search.ja (optional)
missing module named deprecated - imported by mysql.opentelemetry.trace (top-level), mysql.opentelemetry.sdk.trace (top-level), mysql.opentelemetry.sdk.util (top-level), mysql.opentelemetry.sdk.util.instrumentation (top-level)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'dns.resolver' - imported by mysql.connector.pooling (optional)
missing module named dns - imported by mysql.connector.pooling (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
missing module named 'pyarrow._substrait' - imported by pyarrow.substrait (top-level)
missing module named tensorflow - imported by pyarrow.plasma (delayed, optional), pyarrow.conftest (optional)
missing module named 'pyarrow._plasma' - imported by pyarrow.plasma (top-level)
missing module named pyarrow.parquet.ParquetDataset - imported by pyarrow.parquet (delayed), pyarrow.filesystem (delayed)
missing module named awscrt - imported by botocore.compat (optional), botocore.httpchecksum (conditional), botocore.session (delayed, optional)
missing module named 'botocore.vendored.six.moves' - imported by botocore.compat (top-level), botocore.httpsession (top-level), botocore.utils (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), asttokens.asttokens (top-level)
missing module named six.moves.xrange - imported by six.moves (top-level), asttokens.asttokens (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional), PyQt5 (top-level)
missing module named 'PyQt5.QtDataVisualization' - imported by qtpy.QtDataVisualization (conditional, optional)
missing module named Foundation - imported by send2trash.plat_osx_pyobjc (top-level), pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named urlparse - imported by sqlalchemy.util.compat (conditional), pip._vendor.cachecontrol.compat (optional), pip._vendor.distlib.compat (conditional), lxml.html (optional), lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named Sybase - imported by sqlalchemy.dialects.sybase.pysybase (delayed)
missing module named pysqlcipher - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional, optional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named postgresql - imported by sqlalchemy.dialects.postgresql.pypostgresql (delayed)
missing module named pgdb - imported by sqlalchemy.dialects.postgresql.pygresql (delayed)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named 'mx.ODBC' - imported by sqlalchemy.connectors.mxodbc (delayed, conditional)
missing module named mx - imported by sqlalchemy.connectors.mxodbc (delayed, conditional)
missing module named pysqlite2 - imported by nbformat.sign (optional), jupyter_server.services.sessions.sessionmanager (optional), sqlalchemy (top-level), sqlalchemy.dialects.sqlite.pysqlite (delayed, conditional, optional)
missing module named xlwt - imported by pandas.io.excel._xlwt (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed, conditional)
missing module named htmlentitydefs - imported by pip._vendor.distlib.compat (conditional), lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed, conditional)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level), jupyter_server.base.handlers (top-level), notebook.base.handlers (top-level)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named importlib_resources - imported by jsonschema._utils (conditional), matplotlib.style.core (conditional)
missing module named trio.open_process - imported by trio (optional), anyio._backends._trio (optional)
missing module named trio.hazmat - imported by trio (optional), anyio._backends._trio (optional)
missing module named 'curio.meta' - imported by sniffio._impl (delayed, conditional)
missing module named hypothesis - imported by trio._core._run (delayed)
missing module named tputil - imported by trio._core._concat_tb (optional)
missing module named _typeshed - imported by trio._file_io (conditional), trio._path (conditional), prompt_toolkit.eventloop.inputhook (conditional), scipy.optimize._direct_py (conditional), dask.base (conditional), anyio._core._fileio (conditional)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named System - imported by IPython.utils._process_cli (top-level)
missing module named clr - imported by IPython.utils._process_cli (top-level)
missing module named click._unicodefun - imported by click (delayed, optional), black (delayed, optional)
missing module named uvloop - imported by black.concurrency (delayed, optional), anyio._backends._asyncio (delayed, conditional, optional)
missing module named tokenize_rt - imported by black.handle_ipynb_magics (delayed, optional)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named pathlib2 - imported by pickleshare (optional)
missing module named railroad - imported by pyparsing.diagram (top-level), pip._vendor.pyparsing.diagram (top-level)
missing module named matplotlib.axes.Axes - imported by matplotlib.axes (delayed), matplotlib.legend (delayed), matplotlib.projections.geo (top-level), matplotlib.projections.polar (top-level), mpl_toolkits.mplot3d.axes3d (top-level), matplotlib.figure (top-level), matplotlib.pyplot (top-level), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level), keyring.backends.libsecret (optional)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named IPython.ipapi - imported by IPython (delayed, conditional, optional), h5py (delayed, conditional, optional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (top-level)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level), send2trash.plat_gio (top-level), keyring.backends.libsecret (optional)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by IPython.lib.guisupport (delayed), ipykernel.eventloops (delayed)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named 'tornado.platform.select' - imported by zmq.eventloop.minitornado.ioloop (delayed)
missing module named 'tornado.platform.kqueue' - imported by zmq.eventloop.minitornado.ioloop (delayed, conditional)
missing module named 'tornado.platform.epoll' - imported by zmq.eventloop.minitornado.ioloop (delayed, conditional)
missing module named monotime - imported by zmq.eventloop.minitornado.platform.auto (optional)
missing module named 'tornado.stack_context' - imported by zmq.eventloop.zmqstream (optional)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional), zmq.eventloop._deprecated (top-level)
missing module named invoke - imported by paramiko.config (optional)
missing module named gssapi - imported by paramiko.ssh_gss (optional)
missing module named gevent - imported by zmq.green.core (top-level), zmq.green.poll (top-level)
missing module named 'gevent.core' - imported by zmq.green.core (delayed, optional)
missing module named 'gevent.hub' - imported by zmq.green.core (top-level)
missing module named 'gevent.event' - imported by zmq.green.core (top-level)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.libzmq - imported by zmq (delayed, optional)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed), dask.base (delayed, conditional, optional)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.arrayobj (top-level), numba.cpython.mathimpl (top-level), numba.cpython.numbers (top-level), numba.cpython.unicode (top-level), numba.np.ufunc.wrappers (top-level)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named coverage - imported by numba.tests.support (optional)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named git - imported by numba.testing.main (delayed, optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (conditional, optional)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (conditional, optional)
missing module named numba.uint8 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint16 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint32 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint64 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.int64 - imported by numba (top-level), numba.np.random.distributions (top-level)
missing module named numba.float32 - imported by numba (top-level), numba.np.random.generator_core (top-level), numba.np.random.distributions (top-level)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional), dask.array.chunk_types (optional), distributed.protocol.sparse (top-level), dask.array.backends (delayed), pyarrow.serialization (delayed, optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.special._spfun_stats (top-level), scipy.optimize._dual_annealing (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.linalg.bandwidth - imported by scipy.linalg (top-level), scipy.linalg._matfuncs (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._nonlin (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.eig - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.lstsq - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level), scipy.linalg._decomp_polar (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.interpolate._bsplines (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.interpolate._cubic (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), dask.dataframe.io.parquet.fastparquet (optional), pyarrow.conftest (optional)
missing module named cython - imported by pyarrow.conftest (optional)
missing module named torch - imported by distributed.protocol.torch (top-level), pyarrow.serialization (delayed, optional)
missing module named rmm - imported by dask.sizeof (delayed), distributed.protocol.rmm (top-level), distributed.diagnostics.rmm (optional), distributed.worker (optional), distributed.comm.ucx (delayed, optional)
missing module named cupy - imported by dask.array.chunk_types (optional), dask.sizeof (delayed), distributed.protocol.cupy (top-level), dask.dataframe.backends (delayed, optional), dask.array.creation (delayed, conditional), dask.array.routines (delayed, conditional), dask.array.backends (delayed)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named snappy._snappy_cffi - imported by snappy.snappy_cffi (top-level)
missing module named tlz.identity - imported by tlz (top-level), dask.base (top-level), distributed.protocol.compression (top-level)
missing module named copy_reg - imported by tblib.pickling_support (conditional)
missing module named __pypy__ - imported by tblib (optional), msgpack.fallback (conditional), pip._vendor.msgpack.fallback (conditional)
missing module named ipycytoscape - imported by dask.base (delayed, conditional, optional)
missing module named 'fastparquet.writer' - imported by dask.dataframe.io.parquet.fastparquet (optional)
missing module named 'fastparquet.util' - imported by dask.dataframe.io.parquet.fastparquet (optional)
missing module named 'cupyx.scipy' - imported by distributed.protocol.cupy (optional), dask.array.utils (delayed, conditional), dask.array.backends (delayed, optional)
missing module named tlz.frequencies - imported by tlz (top-level), dask.array.utils (top-level), dask.array.core (top-level), dask.bag.core (top-level)
missing module named tlz.concat - imported by tlz (top-level), dask.array.chunk (top-level), dask.array.utils (top-level), dask.array.core (top-level), dask.array.slicing (top-level), dask.delayed (top-level), dask.bag.text (top-level), distributed.utils_comm (top-level), dask.array.routines (top-level), dask.array.gufunc (top-level), dask.array.overlap (top-level)
missing module named tiledb - imported by dask.array.tiledb_io (delayed)
missing module named tlz.sliding_window - imported by tlz (top-level), distributed.comm.tcp (top-level), dask.array.routines (top-level), dask.array.creation (top-level)
missing module named tlz.interleave - imported by tlz (top-level), dask.array.routines (top-level)
missing module named tlz.pluck - imported by tlz (top-level), dask.array.slicing (top-level), distributed.scheduler (top-level), distributed.worker (top-level), dask.bag.core (top-level), dask.array.reductions (top-level)
missing module named tlz.memoize - imported by tlz (top-level), dask.array.slicing (top-level), distributed.dashboard.scheduler (top-level)
missing module named tlz.partition_all - imported by tlz (top-level), dask.dataframe.core (top-level), dask.bag.core (top-level), dask.dataframe.categorical (top-level), distributed.client (top-level), distributed.dashboard.components.worker (top-level), dask.array.reductions (top-level)
missing module named tlz.get - imported by tlz (top-level), dask.array.reductions (top-level), dask.array.overlap (top-level)
missing module named tlz.drop - imported by tlz (top-level), distributed.utils_comm (top-level), dask.array.reductions (top-level)
missing module named tlz.compose - imported by tlz (top-level), dask.bag.core (top-level), dask.array.reductions (top-level)
missing module named tlz.accumulate - imported by tlz (top-level), dask.array.core (top-level), dask.bag.core (top-level), dask.array.rechunk (top-level), dask.array.reductions (top-level)
missing module named crick - imported by distributed.counter (optional), dask.array.percentile (delayed)
missing module named tlz.unique - imported by tlz (top-level), dask.delayed (top-level), dask.dataframe.core (top-level), dask.bag.core (top-level), dask.dataframe.multi (top-level), dask.array.gufunc (top-level)
missing module named cupyx - imported by dask.array.chunk_types (optional)
missing module named tlz.partial - imported by tlz (top-level), dask.array.overlap (top-level)
missing module named tlz.partition - imported by tlz (top-level), dask.array.core (top-level), distributed.scheduler (top-level), dask.dataframe.methods (top-level)
missing module named blosc - imported by partd.compressed (top-level), partd.numpy (top-level)
missing module named fastavro - imported by dask.bag.avro (delayed)
missing module named tlz.valmap - imported by tlz (top-level), distributed.scheduler (top-level), dask.bag.core (top-level), distributed.client (top-level), distributed.diagnostics.progress_stream (top-level), distributed.diagnostics.progress (top-level), distributed.dashboard.components.scheduler (top-level), distributed.diagnostics.progressbar (top-level)
missing module named tlz.topk - imported by tlz (top-level), dask.bag.core (top-level), distributed.stealing (top-level)
missing module named tlz.take - imported by tlz (top-level), dask.bag.core (top-level), dask.dataframe.partitionquantiles (top-level)
missing module named tlz.second - imported by tlz (top-level), distributed.scheduler (top-level), dask.bag.core (top-level)
missing module named tlz.remove - imported by tlz (top-level), dask.dataframe.core (top-level), dask.bag.core (top-level)
missing module named tlz.reduceby - imported by tlz (top-level), dask.bag.core (top-level)
missing module named tlz.peek - imported by tlz (top-level), dask.bag.core (top-level)
missing module named tlz.merge_with - imported by tlz (top-level), distributed.scheduler (top-level), dask.bag.core (top-level)
missing module named tlz.join - imported by tlz (top-level), dask.bag.core (top-level)
missing module named tlz.first - imported by tlz (top-level), dask.array.core (top-level), distributed.scheduler (top-level), dask.dataframe.core (top-level), dask.bag.core (top-level), distributed.client (top-level)
missing module named tlz.count - imported by tlz (top-level), dask.bag.core (top-level)
missing module named cudf - imported by distributed.protocol (delayed), dask.dataframe.backends (delayed, optional)
missing module named dask_cudf - imported by dask.dataframe.backends (delayed)
missing module named tlz.merge_sorted - imported by tlz (top-level), distributed.scheduler (top-level), dask.dataframe.multi (top-level), dask.dataframe.partitionquantiles (top-level)
missing module named mpi4py - imported by h5py._hl.files (delayed)
missing module named zarr - imported by dask.array.core (delayed)
missing module named 'tlz.curried' - imported by dask.array.core (top-level), dask.layers (top-level), dask.bag.core (delayed, conditional), distributed.dashboard.components.scheduler (top-level), distributed.dashboard.utils (top-level), dask.array.overlap (top-level)
missing module named mmh3 - imported by dask.hashing (optional)
missing module named xxhash - imported by dask.hashing (optional)
missing module named cityhash - imported by dask.hashing (optional)
missing module named 'tlz.functoolz' - imported by dask.base (top-level)
missing module named tlz.merge - imported by tlz (top-level), dask.base (top-level), dask.array.slicing (top-level), dask.delayed (top-level), distributed.scheduler (top-level), distributed.core (top-level), dask.array.percentile (top-level), dask.dataframe.core (top-level), dask.bag.core (top-level), dask.dataframe.io.hdf (top-level), dask.dataframe.partitionquantiles (top-level), distributed.utils_comm (top-level), distributed.client (top-level), distributed.cfexecutor (top-level), distributed.deploy.old_ssh (top-level), distributed.diagnostics.progress_stream (top-level), distributed.dashboard.components.worker (top-level), distributed.variable (top-level), dask.array.gufunc (top-level)
missing module named tlz.groupby - imported by tlz (top-level), dask.base (top-level), dask.array.core (top-level), distributed.scheduler (top-level), dask.bag.core (top-level), distributed.utils_comm (top-level), distributed.client (top-level), distributed.diagnostics.progress (top-level)
missing module named tlz.curry - imported by tlz (top-level), dask.base (top-level), dask.delayed (top-level), dask.bag.core (top-level), dask.array.wrap (top-level), distributed.dashboard.components.scheduler (top-level)
missing module named msgpack._cmsgpack - imported by msgpack (conditional, optional)
missing module named '__pypy__.builders' - imported by msgpack.fallback (conditional, optional), pip._vendor.msgpack.fallback (conditional, optional)
missing module named cuml - imported by distributed.protocol (delayed)
missing module named 'cupy.cusparse' - imported by distributed.protocol.cupy (conditional, optional)
missing module named 'cupyx.cusparse' - imported by distributed.protocol.cupy (conditional, optional)
missing module named 'keras.models' - imported by distributed.protocol.keras (delayed)
missing module named keras - imported by distributed.protocol.keras (top-level)
missing module named netCDF4 - imported by distributed.protocol.netcdf4 (top-level)
missing module named ucp - imported by distributed.comm.ucx (delayed, conditional, optional)
missing module named pynvml - imported by distributed.diagnostics.nvml (optional)
missing module named tlz.peekn - imported by tlz (top-level), distributed.worker_state_machine (top-level)
missing module named tlz.pipe - imported by tlz (top-level), distributed.dashboard.components.scheduler (top-level)
missing module named tlz.keymap - imported by tlz (top-level), distributed.worker (top-level), distributed.client (top-level)
missing module named bokeh.models.Panel - imported by bokeh.models (conditional), distributed.dashboard.core (conditional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named bokeh.models.renderers.GlyphRenderer - imported by bokeh.models.renderers (top-level), bokeh.plotting.contour (top-level), bokeh.models.annotations.legends (top-level), bokeh.models.plots (top-level), bokeh.models.tools (top-level), bokeh.plotting.glyph_api (conditional)
missing module named bokeh.models.renderers.ContourRenderer - imported by bokeh.models.renderers (top-level), bokeh.plotting.contour (top-level)
missing module named bokeh.models.widgets.TextInput - imported by bokeh.models.widgets (delayed), bokeh.events (delayed)
missing module named bokeh.models.widgets.ToggleButtonGroup - imported by bokeh.models.widgets (delayed), bokeh.events (delayed)
missing module named bokeh.models.widgets.AbstractButton - imported by bokeh.models.widgets (delayed), bokeh.events (delayed)
missing module named bokeh.models.Plot - imported by bokeh.models (delayed), bokeh.events (delayed), bokeh.plotting._figure (top-level), bokeh.plotting._tools (top-level), bokeh.layouts (top-level)
missing module named bokeh.models.annotations.ColorBar - imported by bokeh.models.annotations (conditional), bokeh.models.renderers.glyph_renderer (delayed, conditional)
missing module named bokeh.models.annotations.ContourColorBar - imported by bokeh.models.annotations (conditional), bokeh.models.renderers.contour_renderer (delayed, conditional)
missing module named bokeh.models.LegendItem - imported by bokeh.models (top-level), bokeh.plotting._legends (top-level)
missing module named bokeh.models.Legend - imported by bokeh.models (top-level), bokeh.plotting._legends (top-level)
missing module named bokeh.models.GlyphRenderer - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._graph (top-level)
missing module named bokeh.models.ColumnDataSource - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._figure (top-level), bokeh.plotting._graph (top-level), bokeh.plotting (top-level), distributed.dashboard.components.scheduler (top-level), distributed.dashboard.components.shared (top-level), distributed.dashboard.components.worker (top-level), distributed.dashboard.components.nvml (top-level), distributed.dashboard.components.rmm (top-level)
missing module named bokeh.models.ColumnarDataSource - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._graph (top-level)
missing module named setproctitle - imported by distributed.proctitle (optional)
missing module named jupyter_server.auth.AllowAllAuthorizer - imported by jupyter_server.auth (delayed, conditional), jupyter_server.base.handlers (delayed, conditional)
missing module named conda._vendor.frozendict.FrozenOrderedDict - imported by conda._vendor.frozendict (top-level), conda.resolve (top-level)
missing module named typeutils - imported by conda._vendor.boltons.setutils (optional)
missing module named boto - imported by conda.gateways.connection.adapters.s3 (optional)
missing module named pip._vendor.msgpack._cmsgpack - imported by pip._vendor.msgpack (conditional, optional)
missing module named 'pip._vendor.urllib3.packages.six.moves' - imported by pip._vendor.urllib3.exceptions (top-level), pip._vendor.urllib3.connection (top-level), pip._vendor.urllib3.util.response (top-level), pip._vendor.urllib3.connectionpool (top-level), pip._vendor.urllib3.request (top-level), pip._vendor.urllib3.util.queue (top-level), pip._vendor.urllib3.poolmanager (top-level)
excluded module named __main__ - imported by pip._vendor.pkg_resources (delayed, optional)
missing module named 'pip._vendor.rich.markdown' - imported by pip._vendor.rich.__main__ (top-level)
missing module named 'lockfile.mkdirlockfile' - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named lockfile - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named 'dbus.mainloop' - imported by keyring.backends.kwallet (optional)
missing module named dbus - imported by keyring.backends.kwallet (optional)
missing module named win32ctypes.core._common - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level), win32ctypes.pywin32.win32cred (top-level)
missing module named win32ctypes.core._authentication - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32cred (top-level)
missing module named win32ctypes.core._time - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._system_information - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._resource - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._dll - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named 'secretstorage.exceptions' - imported by keyring.backends.SecretService (optional)
missing module named secretstorage - imported by keyring.backends.SecretService (optional)
missing module named _abcoll - imported by pip._vendor.distlib.compat (optional)
missing module named HTMLParser - imported by pip._vendor.distlib.compat (conditional)
missing module named httplib - imported by pip._vendor.distlib.compat (conditional)
missing module named pip._vendor.pyparsing.Word - imported by pip._vendor.pyparsing (delayed), pip._vendor.pyparsing.unicode (delayed), pip._vendor.packaging.requirements (top-level)
missing module named pip.__file__ - imported by pip (top-level), pip._internal.build_env (top-level)
missing module named 'pip._vendor.requests.packages.urllib3' - imported by pip._vendor.cachecontrol.compat (optional), conda.gateways.connection (optional)
missing module named utils.rm_rf - imported by utils (top-level), menuinst.linux (top-level), menuinst.darwin (top-level)
missing module named utils.get_executable - imported by utils (top-level), menuinst.linux (top-level)
missing module named custom_tools - imported by menuinst.linux (delayed, optional)
missing module named freedesktop - imported by menuinst.linux (top-level)
missing module named _ruamel_yaml - imported by ruamel.yaml.cyaml (top-level), ruamel.yaml.main (optional), ruamel_yaml.cyaml (top-level), ruamel_yaml.main (optional)
missing module named configobj - imported by ruamel.yaml.util (delayed), ruamel_yaml.util (delayed)
missing module named ordereddict - imported by ruamel.yaml.compat (optional), ruamel_yaml.compat (optional)
missing module named pysat - imported by conda.common._logic (delayed)
missing module named pycryptosat - imported by conda.common._logic (delayed)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named 'cryptography.hazmat.backends.openssl.ed25519' - imported by conda_content_trust.common (top-level)
missing module named archspec - imported by conda.core.index (delayed, optional)
missing module named 'pip._vendor.requests.packages.chardet' - imported by conda.common.compat (delayed, optional)
missing module named 'requests.packages.chardet' - imported by conda.common.compat (delayed, optional)
missing module named asyncssh - imported by distributed.deploy.ssh (delayed, optional)
missing module named gilknocker - imported by distributed.system_monitor (delayed, conditional, optional)
missing module named 'mimesis.schema' - imported by dask.datasets (delayed)
missing module named mimesis - imported by dask.datasets (delayed)
missing module named stacktrace - imported by distributed.profile (delayed)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow._gcsfs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional), pyarrow (optional), tqdm.version (optional)
missing module named pandas.msgpack - imported by pandas (optional), partd.python (optional), partd.numpy (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.ExtensionArray - imported by pandas (conditional), pandas.core.construction (conditional)
missing module named pandas.UInt64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named pandas.Int64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named pandas.Float64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional), pandas.core.internals.blocks (conditional), pandas.core.internals.array_manager (conditional), pandas.core.indexes.datetimes (conditional)
