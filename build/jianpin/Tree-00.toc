('D:/anaconda3/Library/lib/tcl8.6',
 'tcl',
 ['demos', '*.lib', 'tclConfig.sh'],
 'DATA',
 [('tcl\\auto.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\clock.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\history.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\history.tcl', 'DATA'),
  ('tcl\\init.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\package.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\package.tcl', 'DATA'),
  ('tcl\\parray.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\parray.tcl', 'DATA'),
  ('tcl\\safe.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tclIndex', 'D:/anaconda3/Library/lib/tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tm.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\word.tcl', 'D:/anaconda3/Library/lib/tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\CET', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\CET', 'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\EET', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\EET', 'DATA'),
  ('tcl\\tzdata\\Egypt',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\EST', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\EST', 'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\GB', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\GMT', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT', 'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\HST', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\HST', 'DATA'),
  ('tcl\\tzdata\\Iceland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\MET', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\MET', 'DATA'),
  ('tcl\\tzdata\\MST', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\MST', 'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\PRC', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\PRC', 'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\ROC', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\ROC', 'DATA'),
  ('tcl\\tzdata\\ROK', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\ROK', 'DATA'),
  ('tcl\\tzdata\\Singapore',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\UCT', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\UCT', 'DATA'),
  ('tcl\\tzdata\\Universal',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\UTC', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\UTC', 'DATA'),
  ('tcl\\tzdata\\W-SU',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\WET', 'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\WET', 'DATA'),
  ('tcl\\tzdata\\Zulu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'D:/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'D:/anaconda3/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'D:/anaconda3/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'D:/anaconda3/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'D:/anaconda3/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'D:/anaconda3/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'D:/anaconda3/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA')])
