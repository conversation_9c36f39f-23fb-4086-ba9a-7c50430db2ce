# encoding: utf-8
# @File  : demo4.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/04/02 18:39
import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(viewport={"width":1280,"height":950})
    page = context.new_page()
    page.goto("https://lxerptest2.66123123.com/")
    page.goto("https://lxerptest2.66123123.com/#/")
    page.goto("https://lxerptest2.66123123.com/#/login?redirect=%2Fhomepage%2Fhome%2Fhomeindex")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("liujianqiang")
    page.get_by_placeholder("请输入账号").press("Tab")
    page.get_by_placeholder("请输入密码").fill("a111111")
    page.get_by_placeholder("请输入密码").press("Enter")
    page.get_by_role("button", name="添 加").click()
    page.get_by_role("menuitem", name="订单").click()
    page.get_by_text("新增派单").click()
    page.locator(".el-input__inner").first.click()
    page.locator(".el-input__inner").first.fill("刘建强")
    page.get_by_text("刘建强专柜项目").click()
    page.locator("div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
    page.get_by_text("专柜项目02").click()
    page.locator("div").filter(has_text=re.compile(r"^客户部门 专柜项目02$")).get_by_placeholder("请输入").click()
    page.locator("ul").filter(has_text=re.compile(r"^专柜项目02$")).locator("span").click()
    page.get_by_placeholder("请输入内容").click()
    page.get_by_placeholder("请输入内容").fill("1")
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
    page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill("1")
    page.locator(".basic_mess > div:nth-child(2) > div:nth-child(3) > div").click()
    page.get_by_placeholder("省/直辖市").click()
    page.get_by_text("天津市").click()
    page.get_by_placeholder("城市").click()
    page.locator("ul").filter(has_text=re.compile(r"^天津市$")).locator("span").click()
    page.get_by_placeholder("区/县").click()
    page.get_by_text("河北区").click()
    page.get_by_placeholder("请输入200字以内").click()
    page.get_by_placeholder("请输入200字以内").fill("放大点")
    page.get_by_role("button", name="确 定").click()
    page.get_by_role("row", name="商品合计： ￥0.00    应付款： ￥").get_by_placeholder("请输入").first.click()
    page.get_by_role("row", name="商品合计： ￥0.00    应付款： ￥").get_by_placeholder("请输入").first.fill("1")
    page.get_by_text("洒哇地咔 Swdk S1-1").nth(1).click()
    page.get_by_role("button", name="保存订单").click()
    page.get_by_role("button", name="确 定").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
