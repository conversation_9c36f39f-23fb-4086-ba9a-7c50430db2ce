"""
@File        : test_add_order_excel.py
<AUTHOR> yangxue
@Date        : 2025-05-13
@Description : 支持 Excel 驱动的新增接单--发货脚本
"""
import re
import time

import allure
import pytest
import logging
from common.attach import screenshot_after_step
from common.ui_action import select_project, select_address, normalize_input_data

logger = logging.getLogger(__name__)

pytestmark = [
    pytest.mark.order(3),
    pytest.mark.use_excel,  # ✅ 启用 Excel 驱动标记
    pytest.mark.warehouse(enable=False, disable=True),
]

@allure.feature("订单管理")
@allure.story("新增接单-订单发货")
def test_add_order_excel(setup_warehouse, test_data_excel, get_mark_account, login_web, order_no_storage):
    set_order_no, _ = order_no_storage
    page = login_web
    with allure.step("进入新增接单页面"):
        # ✅ 每轮都回首页重新点菜单，保证环境干净
        page.get_by_role("menuitem", name="首页").click()
        page.get_by_role("menuitem", name="订单").click()
        page.get_by_text("新增接单", exact=True).first.click()
        page.wait_for_selector("text=项目", timeout=5000)
        screenshot_after_step(page, description="进入新增接单页面")
    for case in test_data_excel:
        input_data = case["input"]
        logger.info(f"\n\n📦 开始执行: {case['description']}")
        # with allure.step("进入新增接单页面"):
        #     # ✅ 每轮都回首页重新点菜单，保证环境干净
        #     page.get_by_role("menuitem", name="首页").click()
        #     page.get_by_role("menuitem", name="订单").click()
        #     page.get_by_text("新增接单", exact=True).first.click()
        #     page.wait_for_selector("text=项目", timeout=5000)
        #     screenshot_after_step(page, description="进入新增接单页面")
        # **填写基本信息**
        with allure.step("填写基本信息"):
            select_project(page, input_data["project"])
            page.locator(
                "div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").first.click()
            page.get_by_text(input_data["customer"]).click()
            page.get_by_placeholder("请输入内容").click()
            page.get_by_placeholder("请输入内容").fill(input_data["contact"]["name"])
            page.locator(".el-form-item__content > .el-input > .el-input__inner").first.click()
            page.locator(".el-form-item__content > .el-input > .el-input__inner").first.fill(
                input_data["contact"]["phone"])
            select_address(page, input_data["address"]["province"], input_data["address"]["city"], input_data["address"]["district"])
            page.get_by_placeholder("请输入200字以内").click()
            page.get_by_placeholder("请输入200字以内").fill(input_data["address"]["detail"])
            page.get_by_role("button", name="确 定").click()
            screenshot_after_step(page, description="填写基本信息")
        # **填写商务员**
        with allure.step("填写商务员"):
            page.locator(
                "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
            page.locator(
                "div:nth-child(3) > div:nth-child(2) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
                "yx")
            page.get_by_text(input_data["business_staff"]["primary"]).click()

            page.locator(
                "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").click()
            page.locator(
                "div:nth-child(3) > div:nth-child(3) > .el-form-item > .el-form-item__content > .el-select > .el-input > .el-input__inner").fill(
                "yx")
            page.get_by_text(input_data["business_staff"]["secondary"]).nth(1).click()
            page.get_by_placeholder("字数长度1~200").click()
            page.get_by_placeholder("字数长度1~200").fill("自动化测试")
        with allure.step("填写商品信息"):
            product = input_data["products"][0]
            # 填写商品编码
            page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).click()
            page.get_by_role("row", name="--").get_by_placeholder("请输入").nth(1).fill(product["sku"])
            page.get_by_text(product["sku"]).nth(3).click()

            # 填写单价
            page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).click()
            page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).press(
                "ControlOrMeta+a")
            page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(1).fill(
                str(product["price"]))

            # 填写数量
            page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).click()
            page.get_by_role("row", name=re.compile(product["sku"])).get_by_placeholder("字数长度1~").nth(2).fill(
                str(product["quantity"]))

        with allure.step("保存订单"):
            page.get_by_role("button", name="保存订单").click()
            order_no_element = page.locator("#inviteCode")
            order_no = order_no_element.inner_text()
            logger.info(f"✅ 订单编号提取成功: {order_no}")
            set_order_no(order_no)  # 存入 pytest fixture 和 JSON
            # ✅ 关闭弹框
            page.get_by_role("button", name="确 定").click()
            # page.get_by_text("新增接单", exact=True).first.click()




