# encoding: utf-8
# @File  : jiami.py
# @Author: liujq
# @Desc : 
# @Date  :  2025/04/02 14:51
# -*- coding:utf-8 -*-
#Author: liujq
#Time  : 2021/6/8 12:45
import subprocess

from jpype import *
import jpype
import os
import requests
import jsonpath
import json
import sys

# 启动 并指定JAR包
def ERP_login_interface(env,username,password):
    jar_path = r"./leading-encrypt-1.0.7-SNAPSHOT-jar-with-dependencies.jar"
    jar_path = os.path.join(os.getcwd(),'leading-encrypt-1.0.7-SNAPSHOT-jar-with-dependencies.jar')
    # print(os.path.exists(jar_path))
    print(jar_path)

    jpype.startJVM(jpype.getDefaultJVMPath(), "-ea", fr"-Djava.class.path={jar_path}")
    #jpype.startJVM(jpype.getDefaultJVMPath(), "-ea", f'-Djava.class.path={jar_path}')

    url='https://'+env+'/api/authorization-server/login/key/erpWeb'
    res1=requests.get(url=url)
    print(res1.json())
    print(res1.elapsed.total_seconds() * 1000)

    pub_key=jsonpath.jsonpath(res1.json(),'$..publicKey')[0]
    key=jsonpath.jsonpath(res1.json(),'$..key')[0]

    str_data=key+password
    print(str_data)
    JClass = jpype.JClass("com.leading.encrypt.sm.SM2Utils")
    jc=JClass()

    res = str(jc.encryptHex(pub_key, str_data))

    # print(type(res))
    print(res)
    #header={'Authorization':'Basic V0VCQVBQOldFQkFQUA==','channel':'erpWeb','key':key,'mac':'309C23FA481A'}
    header={'Authorization':'Basic V0VCQVBQOldFQkFQUA==','channel':'erpWeb','key':key,'mac':'02f21e70-77d9-1e89-b804-fe822f1e063f'}
    # print(header)



    #body=f'grant_type=password&username=liujianqiang&password={res}'
    body={'grant_type':'password','username':username,'password':res}
    print(body)
    res2=requests.post(url='https://'+env+'/api/authorization-server/oauth/token',data=body,headers=header)
    print(res2.json())
    jpype.shutdownJVM()
    return jsonpath.jsonpath(res2.json(),'$..access_token')[0]


if __name__ == '__main__':

    # env=sys.argv[1]
    # user=sys.argv[2]
    # psw=sys.argv[3]
    # print(env,user,psw)
    #res = ERP_login_interface(env, user, psw)
    res=ERP_login_interface('lxerp.66123123.com','liangdan05','lxwl8888')
    #res=ERP_login_interface('lxerp.66123123.com','liujianqiang','abc123')


    print(res)
    # print(res.elapsed.total_seconds() * 1000)




# encoding: utf-8
# @File  : jianpin.py
# @Author: liujq
# @Desc :
# @Date  :  2025/04/02 10:03
import tkinter as tk
from tkinter import messagebox, simpledialog, filedialog
import requests
import pandas as pd
import mysql.connector
import jpype
import os
import requests
import jsonpath
from tkinter import ttk

# 假设有一个函数可以从数据库查询 createUserId

# 全局变量用于存储JVM状态
jvm_started = False

def start_jvm():
    global jvm_started
    if not jvm_started:
        jar_path = os.path.join(os.getcwd(), 'leading-encrypt-1.0.7-SNAPSHOT-jar-with-dependencies.jar')
        jpype.startJVM(jpype.getDefaultJVMPath(), "-ea", fr"-Djava.class.path={jar_path}")
        jvm_started = True

# 新增登录接口调用函数
def login(username, password):
    start_jvm()  # 确保JVM只启动一次

    url = 'https://lxerp.66123123.com/api/authorization-server/login/key/erpWeb'
    res1 = requests.get(url=url)
    print(res1.json())
    print(res1.elapsed.total_seconds() * 1000)

    pub_key = jsonpath.jsonpath(res1.json(), '$..publicKey')[0]
    key = jsonpath.jsonpath(res1.json(), '$..key')[0]

    str_data = key + password
    print(str_data)
    JClass = jpype.JClass("com.leading.encrypt.sm.SM2Utils")
    jc = JClass()

    res = str(jc.encryptHex(pub_key, str_data))

    header = {'Authorization': 'Basic V0VCQVBQOldFQkFQUA==', 'channel': 'erpWeb', 'key': key, 'mac': '02f21e70-77d9-1e89-b804-fe822f1e063f'}
    body = {'grant_type': 'password', 'username': username, 'password': res}
    print(body)
    res2 = requests.post(url='https://lxerp.66123123.com/api/authorization-server/oauth/token', data=body,
                         headers=header)
    print(res2.json())
    access_tokens = jsonpath.jsonpath(res2.json(), '$..access_token')
    print(access_tokens)
    print('Bearer ' + access_tokens[0])
    if isinstance(access_tokens, list) and access_tokens:
        return 'Bearer ' + access_tokens[0]
    else:
        raise Exception("Failed to retrieve access token")

# 修改后的调用接口函数，增加Authorization头
def call_api(params, token):
    if not token:
        raise Exception("Token is missing or invalid")

    #print(f"Using token: {token}")  # 打印 token 用于调试
    print(params)
    url = "https://lxerp.66123123.com/api/product-service/sku/intelligent-build/batch"
    headers = {
        "Authorization": token  # 使用 f-string 格式化字符串
    }
    #print(f"Headers: {headers}")  # 打印 headers 用于调试

    response = requests.post(url, json=params, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Response content: {response.content}")  # 打印响应内容用于调试
        raise Exception(f"API request failed with status code {response.status_code}")

# 导出数据到Excel文件
def export_to_excel(data):
    df = pd.DataFrame(data)
    file_path = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel files", "*.xlsx")])
    if file_path:
        df.to_excel(file_path, index=False)
        messagebox.showinfo("导出成功", f"文件导入到 {file_path}")
    else:
        messagebox.showwarning("导出失败", "没有查询到需要导出的文件路径")

# 新增数据库处理类
class DatabaseHandler:
    def __init__(self, host, user, password, database, port=3306):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.port = port
        self.connection = None

    def connect(self):
        try:
            if self.connection is None or not self.connection.is_connected():
                self.connection = mysql.connector.connect(
                    host=self.host,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    port=self.port
                )
                print("数据库已连接")
        except mysql.connector.Error as err:
            print(f"Error: {err}")

    def disconnect(self):
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("数据库已关闭")

    def execute_query(self, query, params=None):
        self.connect()  # 确保每次执行查询时连接是有效的
        cursor = self.connection.cursor()
        try:
            cursor.execute(query, params or ())
            result = cursor.fetchall()
            return result
        except mysql.connector.Error as err:
            print(f"Error: {err}")
            return None
        finally:
            cursor.close()

# 主界面类
class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("ERP liangdan05 建品工具")
        self.geometry("1500x800")  # 增大窗口尺寸

        # 初始化数据库处理器
        self.db_handler_query = DatabaseHandler(
            host="***************",
            user="website_leading_ro",
            password="##@leadingURP45",
            database="LeadingDB",
            port=3306
        )

        self.db_handler_execute = DatabaseHandler(
            host="***************",
            user="website_leading_ro",
            password="##@leadingURP45",
            database="userdb",
            port=3306
        )

        # 创建控件
        self.create_widgets()

    def create_widgets(self):
        # 创建表单区域框架
        form_frame = tk.Frame(self, bg="lightgray", padx=30, pady=30)  # 增加内边距
        form_frame.grid(row=0, column=0, sticky="nsew")

        # 用户名输入框
        tk.Label(form_frame, text="Username:", bg="lightgray").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.username_entry = tk.Entry(form_frame)
        self.username_entry.insert(0, "liangdan05")
        self.username_entry.grid(row=0, column=1, padx=5, pady=5)

        # 密码输入框
        tk.Label(form_frame, text="Password:", bg="lightgray").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.password_entry = tk.Entry(form_frame, show="*")
        self.password_entry.insert(0, "lxwl8888")
        self.password_entry.grid(row=1, column=1, padx=5, pady=5)

        # ID范围输入框
        tk.Label(form_frame, text="Start ID:", bg="lightgray").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        self.id_start_entry = tk.Entry(form_frame)
        self.id_start_entry.grid(row=2, column=1, padx=5, pady=5)

        tk.Label(form_frame, text="End ID:", bg="lightgray").grid(row=3, column=0, padx=5, pady=5, sticky="e")
        self.id_end_entry = tk.Entry(form_frame)
        self.id_end_entry.grid(row=3, column=1, padx=5, pady=5)

        # 查询按钮和执行按钮放在同一行
        self.query_button = tk.Button(form_frame, text="根据id起始值查询", command=self.query_data)
        self.query_button.grid(row=4, column=0, padx=5, pady=10)

        self.execute_button = tk.Button(form_frame, text="执行", command=self.execute_query)
        self.execute_button.grid(row=4, column=1, padx=5, pady=10)

        # 结果展示区域
        result_frame = tk.Frame(self, bg="white", padx=20, pady=20, height=900)  # 增加结果展示区域的高度
        result_frame.grid(row=6, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)

        # 创建 Treeview 组件
        self.tree = ttk.Treeview(result_frame, columns=("ser_no", "brand_name", "model", "product_name", "unit", "jd_url", "purchase_price", "website_price", "market_price", "res"), show="headings",height=20)
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 设置列标题
        self.tree.heading("ser_no", text="序号")
        self.tree.column("ser_no", width=50)
        self.tree.heading("brand_name", text="品牌名称")
        self.tree.heading("model", text="型号")
        self.tree.heading("product_name", text="产品名称")
        self.tree.heading("unit", text="单位")
        self.tree.column("unit", width=60)
        self.tree.heading("jd_url", text="京东链接")
        self.tree.heading("purchase_price", text="采购价格")
        self.tree.column("purchase_price", width=50)
        self.tree.heading("website_price", text="网站价格")
        self.tree.column("purchase_price", width=50)
        self.tree.heading("market_price", text="市场价")
        self.tree.column("purchase_price", width=50)
        self.tree.heading("res", text="执行结果")
        self.tree.column("res", width=60)

        # 添加垂直滚动条
        scrollbar_y = tk.Scrollbar(result_frame, orient=tk.VERTICAL)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置 Treeview 和滚动条
        self.tree.config(yscrollcommand=scrollbar_y.set)
        scrollbar_y.config(command=self.tree.yview)

        # 导出按钮
        self.export_button = tk.Button(self, text="导出执行结果", command=self.export_data)
        self.export_button.grid(row=7, column=0, columnspan=2, pady=10)

    def query_data(self):
        id_start = self.id_start_entry.get()
        id_end = self.id_end_entry.get()

        if not all([id_start, id_end]):
            messagebox.showwarning("Input Error", "Please fill in ID start and end fields.")
            return

        try:
            id_start = int(id_start)
            id_end = int(id_end)
        except ValueError:
            messagebox.showwarning("Input Error", "ID values must be integers.")
            return

        # 清空 Treeview
        for i in self.tree.get_children():
            self.tree.delete(i)

        query = "SELECT * FROM `LeadingDB`.`product_create` WHERE ser_no BETWEEN %s AND %s"
        result = self.db_handler_query.execute_query(query, (id_start, id_end))

        if result:
            # 插入新数据
            for row in result:
                self.tree.insert("", tk.END, values=row)
        else:
            messagebox.showinfo("Query Result", "No data found for the given ID range.")

        # 关闭数据库连接
        self.db_handler_query.disconnect()

    def get_create_user_id(self, username):
        # 查询数据库获取用户ID
        query = "SELECT id FROM userdb.account WHERE userName = %s"
        result = self.db_handler_execute.execute_query(query, (username,))  # 使用 db_handler_execute
        if result:
            return result[0][0]
        else:
            return None

    def execute_query(self):
        username = self.username_entry.get()
        password = self.password_entry.get()
        id_start = self.id_start_entry.get()
        id_end = self.id_end_entry.get()

        if not all([username, password, id_start, id_end]):
            messagebox.showwarning("Input Error", "Please fill in all fields.")
            return

        try:
            id_start = int(id_start)
            id_end = int(id_end)
        except ValueError:
            messagebox.showwarning("Input Error", "ID values must be integers.")
            return

        create_user_id = self.get_create_user_id(username)
        if create_user_id is None:
            messagebox.showwarning("User Not Found", "User not found in the database.")
            return

        try:
            # 调用登录接口获取token
            token = login(username, password)
        except Exception as e:
            messagebox.showerror("Login Error", str(e))
            return

        params = {
            "idStart": id_start,
            "idEnd": id_end,
            "createUserId": create_user_id,
            "isModel": True,
            "isSkuName": True,
            "isUnit": True
        }

        try:
            # 调用API并传入token
            result = call_api(params, token)

            # 弹出提示框显示返回内容
            messagebox.showinfo("执行结果", str(result))

        except Exception as e:
            messagebox.showerror("执行接口错误", str(e))

    def export_data(self):
        # 从 Treeview 中获取所有行的数据
        items = self.tree.get_children()
        if not items:
            messagebox.showwarning("No Data", "No data to export.")
            return

        data = []
        for item in items:
            values = self.tree.item(item, 'values')
            data.append(values)

        if data:
            try:
                export_to_excel(data)
            except Exception as e:
                messagebox.showerror("Export Error", str(e))
        else:
            messagebox.showwarning("No Data", "No data to export.")

if __name__ == "__main__":
    # app = App()
    # app.mainloop()
    #login('liangdan05','lxwl8888')
    #login


    import requests
    body=   {
     "name": "Laptop",
     "description": "A high-end gaming laptop",
     "price": 1500.0,
     "tax": 120.0
   }
    response = requests.post('http://127.0.0.1:8000/items/', json=body)
    print(response.json())